name: Release

on:
  push:
    tags:
      - "v*"

permissions:
  contents: write
  packages: write

jobs:
  release:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: "18"
          cache: "npm"

      - name: Setup Python
        uses: actions/setup-python@v5
        with:
          python-version: "3.13"

      - name: Install UV
        run: curl -LsSf https://astral.sh/uv/install.sh | sh

      - name: Install dependencies
        run: |
          npm install -g pnpm
          pnpm install
          cd packages/backend && uv sync

      - name: Run tests
        run: |
          pnpm test:frontend
          pnpm test:backend

      - name: Build packages
        run: |
          pnpm build:frontend
          pnpm build:backend

      - name: Extract release notes
        id: extract_notes
        run: |
          VERSION=${GITHUB_REF#refs/tags/v}
          echo "VERSION=$VERSION" >> $GITHUB_OUTPUT

          # Extract changelog section for this version
          awk "/^## \[$VERSION\]/ { flag=1; next } /^## \[/ && flag { exit } flag" CHANGELOG.md > release_notes.md

          # Remove date line and clean up
          sed -i '1s/ - [0-9]\{4\}-[0-9]\{2\}-[0-9]\{2\}$//' release_notes.md

          echo "RELEASE_NOTES<<EOF" >> $GITHUB_OUTPUT
          cat release_notes.md >> $GITHUB_OUTPUT
          echo "EOF" >> $GITHUB_OUTPUT

      - name: Create GitHub Release
        uses: softprops/action-gh-release@v2
        with:
          name: Release ${{ steps.extract_notes.outputs.VERSION }}
          body: ${{ steps.extract_notes.outputs.RELEASE_NOTES }}
          draft: false
          prerelease: false

      - name: Build and push Docker images
        env:
          REGISTRY: ghcr.io
          IMAGE_NAME: ${{ github.repository }}
        run: |
          echo ${{ secrets.GITHUB_TOKEN }} | docker login $REGISTRY -u ${{ github.actor }} --password-stdin

          # Build and tag images
          VERSION=${{ steps.extract_notes.outputs.VERSION }}

          # Frontend image
          docker build -f frontend/Dockerfile.prod -t $REGISTRY/$IMAGE_NAME/frontend:$VERSION -t $REGISTRY/$IMAGE_NAME/frontend:latest .
          docker push $REGISTRY/$IMAGE_NAME/frontend:$VERSION
          docker push $REGISTRY/$IMAGE_NAME/frontend:latest

          # Backend image  
          docker build -f packages/backend/Dockerfile.prod -t $REGISTRY/$IMAGE_NAME/backend:$VERSION -t $REGISTRY/$IMAGE_NAME/backend:latest .
          docker push $REGISTRY/$IMAGE_NAME/backend:$VERSION
          docker push $REGISTRY/$IMAGE_NAME/backend:latest

          # Demo processing image
          docker build -f packages/demo-processing/Dockerfile -t $REGISTRY/$IMAGE_NAME/demo-processing:$VERSION -t $REGISTRY/$IMAGE_NAME/demo-processing:latest .
          docker push $REGISTRY/$IMAGE_NAME/demo-processing:$VERSION  
          docker push $REGISTRY/$IMAGE_NAME/demo-processing:latest

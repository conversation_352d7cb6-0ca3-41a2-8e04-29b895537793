# Development Environment Configuration
ENV=development

# Backend
DATABASE_URI=sqlite:///./database.db
DEMO_STORAGE_PATH=./storage/demos
REDIS_URL=redis://localhost:6379
SECRET_KEY=dev-secret-key-not-for-production

# Demo Processing
DEMO_PROCESSING_REDIS_URL=redis://localhost:6379
DEMO_PROCESSING_LOG_LEVEL=DEBUG
DEMO_PROCESSING_SERVICE_NAME=demo-processing-dev
DEMO_PROCESSING_MAX_CONCURRENT_PROCESSING=2
DEMO_PROCESSING_PROCESSING_TIMEOUT_SECONDS=300
DEMO_PROCESSING_EXECUTOR_MAX_WORKERS=2

# Frontend
DATABASE_URL=local.db
BACKEND_API_URL=http://localhost:8000
NODE_ENV=development

# Docker
COMPOSE_PROJECT_NAME=brainless-stats-dev

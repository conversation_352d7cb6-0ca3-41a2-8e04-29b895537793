"""Pydantic models for demo processing messages and data structures."""

from pydantic import BaseModel, Field


class DemoProcessingRequest(BaseModel):
    """Request model for processing a demo file."""

    demo_file_path: str = Field(..., description="Path to the demo file to process")
    request_id: str = Field(
        ..., description="Unique identifier for this processing request"
    )
    user_id: str | None = Field(
        None, description="User ID who requested the processing"
    )


class PlayerData(BaseModel):
    """Player data extracted from demo."""

    steam_id: str = Field(..., description="Steam ID of the player")
    name: str = Field(..., description="Player name")
    team: str = Field(..., description="Team name (CT/T)")


class RoundData(BaseModel):
    """Round data extracted from demo."""

    round_number: int = Field(..., description="Round number")
    winner: str = Field(..., description="Winning team")
    win_reason: str = Field(..., description="Reason for win")
    ct_score: int = Field(..., description="Counter-Terrorist score")
    t_score: int = Field(..., description="Terrorist score")


class MapData(BaseModel):
    """Map data extracted from demo."""

    name: str = Field(..., description="Map name")


class DemoData(BaseModel):
    """Complete demo data structure."""

    map_info: MapData = Field(..., description="Map information")
    players: list[PlayerData] = Field(..., description="List of players")
    rounds: list[RoundData] = Field(..., description="List of rounds")
    match_info: dict[str, str | int | float | bool] = Field(
        ..., description="Additional match information"
    )


class DemoProcessingResult(BaseModel):
    """Result of demo processing."""

    request_id: str = Field(..., description="Request ID that was processed")
    success: bool = Field(..., description="Whether processing was successful")
    demo_data: DemoData | None = Field(None, description="Extracted demo data")
    error_message: str | None = Field(
        None, description="Error message if processing failed"
    )
    processing_time_seconds: float = Field(
        ..., description="Time taken to process the demo"
    )


class DemoProcessingError(BaseModel):
    """Error response for failed demo processing."""

    request_id: str = Field(..., description="Request ID that failed")
    error_type: str = Field(..., description="Type of error")
    error_message: str = Field(..., description="Detailed error message")
    timestamp: str = Field(..., description="Timestamp of the error")

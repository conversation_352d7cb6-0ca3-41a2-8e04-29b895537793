"""Main entry point for the demo processing service."""

import sys

from .config import get_settings
from .logging_config import configure_logging, get_logger
from .service import create_app, run_service

logger = get_logger(__name__)


def main() -> None:
    """Main entry point for the demo processing service."""
    settings = get_settings()

    # Setup logging
    configure_logging(settings.log_level)

    logger.info(
        "Starting service",
        service_name=settings.service_name,
        redis_url=settings.redis_url,
        max_concurrent_processing=settings.max_concurrent_processing,
    )

    try:
        # Create and configure the app
        _ = create_app(broker_url=settings.redis_url)

        logger.info("Demo processing service is ready")
        run_service()

    except KeyboardInterrupt:
        logger.info("Service interrupted by user")
        sys.exit(0)
    except Exception as e:
        # Check if it's a Redis connection error
        if "redis" in str(e).lower() or "timeout" in str(e).lower():
            logger.exception(
                "Failed to connect to Redis. Please ensure Red<PERSON> is running and accessible.",
                redis_url=settings.redis_url,
                error=str(e),
            )
        else:
            logger.exception("Failed to start service", error=str(e))
        sys.exit(1)


if __name__ == "__main__":
    main()

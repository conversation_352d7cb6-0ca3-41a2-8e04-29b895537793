"""Async utilities for demo processing using AnyIO."""

import time
from collections.abc import AsyncGenerator
from contextlib import asynccontextmanager

from anyio import CapacityLimiter, Semaphore

from .config import get_settings
from .logging_config import get_logger

logger = get_logger(__name__)

# Global semaphore for concurrency control
_processing_semaphore: Semaphore | None = None
_thread_limiter: CapacityLimiter | None = None


def get_processing_semaphore() -> Semaphore:
    """Get or create the processing semaphore for concurrency control.

    Returns:
        AnyIO Semaphore for limiting concurrent processing
    """
    global _processing_semaphore
    if _processing_semaphore is None:
        settings = get_settings()
        _processing_semaphore = Semaphore(settings.max_concurrent_processing)
    return _processing_semaphore


def get_thread_limiter() -> CapacityLimiter:
    """Get or create the thread capacity limiter for CPU-bound tasks.

    Returns:
        AnyIO CapacityLimiter for limiting thread usage
    """
    global _thread_limiter
    if _thread_limiter is None:
        settings = get_settings()
        _thread_limiter = CapacityLimiter(settings.executor_max_workers)
    return _thread_limiter


@asynccontextmanager
async def processing_slot() -> AsyncGenerator[None]:
    """Context manager for acquiring a processing slot.

    This ensures that only a limited number of demos are processed concurrently.
    """
    semaphore = get_processing_semaphore()
    async with semaphore:
        logger.debug("Acquired processing slot")
        try:
            yield
        finally:
            logger.debug("Released processing slot")


class ProgressTracker:
    """Simple progress tracker for long-running operations."""

    def __init__(self, operation_id: str, total_steps: int = 100) -> None:
        """Initialize progress tracker.

        Args:
            operation_id: Unique identifier for the operation
            total_steps: Total number of steps (for percentage calculation)
        """
        self.operation_id: str = operation_id
        self.total_steps: int = total_steps
        self.current_step: int = 0
        self.start_time: float = time.time()
        self.last_update_time: float = self.start_time

    def update(self, step: int, message: str = "") -> None:
        """Update progress.

        Args:
            step: Current step number
            message: Optional progress message
        """
        self.current_step = step
        current_time = time.time()
        elapsed = current_time - self.start_time

        # Only log progress updates every 5 seconds to avoid spam
        if current_time - self.last_update_time >= 5.0:
            percentage = (step / self.total_steps) * 100
            logger.info(
                "Progress update",
                operation_id=self.operation_id,
                step=step,
                total_steps=self.total_steps,
                percentage=f"{percentage:.1f}%",
                elapsed_seconds=f"{elapsed:.1f}",
                message=message,
            )
            self.last_update_time = current_time

    def complete(self, message: str = "Operation completed") -> None:
        """Mark operation as complete.

        Args:
            message: Completion message
        """
        elapsed = time.time() - self.start_time
        logger.info(
            "Operation completed",
            operation_id=self.operation_id,
            total_time_seconds=f"{elapsed:.2f}",
            message=message,
        )

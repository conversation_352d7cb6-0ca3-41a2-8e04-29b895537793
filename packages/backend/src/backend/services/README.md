# Demo Parser Service

This service provides an interface for parsing CS2 demo files using the `demoparser2` and `awpy` libraries.

## Overview

The demo parser service extracts basic match data from CS2 demo files, including:

- Map information
- Player details
- Round results

## Usage

### Basic Usage

```python
from backend.services.demo_parser import DemoParser, get_demo_parser
from backend.demo_storage import get_demo_storage

# Get dependencies
demo_storage = get_demo_storage()
demo_parser = DemoParser(demo_storage)

# Parse a demo file
match_data = demo_parser.parse_demo("/path/to/demo.dem")

# Access the parsed data
map_name = match_data["map"]["name"]
players = match_data["players"]
rounds = match_data["rounds"]
```

### Using with FastAPI Dependency Injection

```python
from typing import Annotated

from fastapi import Depends, FastAPI
from backend.services.demo_parser import DemoParser, get_demo_parser

app = FastAPI()

@app.get("/parse-demo")
async def parse_demo(
    demo_path: str,
    parser: Annotated[Demo<PERSON>arser, Depends(get_demo_parser)]
):
    match_data = parser.parse_demo(demo_path)
    return match_data
```

## Error Handling

The service provides a custom exception `DemoParserError` for handling parsing errors:

```python
from backend.services.demo_parser import DemoParser, DemoParserError

parser = DemoParser()

try:
    match_data = parser.parse_demo("/path/to/demo.dem")
except DemoParserError as e:
    print(f"Error parsing demo: {e}")
```

## Data Structure

The parsed match data has the following structure:

```python
{
    "map": {
        "name": "de_dust2"
    },
    "players": [
        {
            "steam_id": "76561198123456789",
            "name": "Player1",
            "team": "CT"
        },
        # More players...
    ],
    "rounds": [
        {
            "round_number": 1,
            "winner": "CT",
            "win_reason": "ALL_TERRORISTS_KILLED",
            "ct_score": 1,
            "t_score": 0
        },
        # More rounds...
    ],
    "match_info": {
        # Raw match info from the parser
    }
}
```

## Dependencies

- `demoparser2`: High-performance demo parser for CS2
- `awpy`: Python library for parsing CS2 demo files

from functools import lru_cache
from pathlib import Path
from typing import Literal

from brainless_core.config import BaseAppConfig
from pydantic import field_validator


class Settings(BaseAppConfig):
    """Settings for the backend application."""

    # Environment override
    environment: Literal["development", "staging", "production"] = "development"
    service_name: str = "brainless-backend"

    # Backend-specific settings
    steam_api_key: str = ""  # Must be set in .env file
    demo_storage_path: Path = Path("./storage/demos").absolute()
    demo_retention_days: int = 7  # Number of days to keep processed demos
    request_timeout: int = 10  # Timeout in seconds for API requests

    # Database settings (inheriting from DatabaseConfig)
    database_url: str = "sqlite:///./database.db"

    # Redis settings (inheriting from RedisConfig)
    redis_url: str = "redis://localhost:6379"

    @field_validator("demo_storage_path")
    @classmethod
    def ensure_data_dirs_exist(cls, v: Path) -> Path:
        """Ensure data directories exist."""
        v.mkdir(parents=True, exist_ok=True)
        return v


@lru_cache
def get_settings() -> Settings:
    """Get application settings.

    Returns:
        Settings instance
    """
    return Settings()


# Keep the old settings instance for backward compatibility during transition
settings = get_settings()

from collections.abc import AsyncGenerator, Generator
from typing import override

import pytest
from httpx import ASGITransport, AsyncClient
from sqlalchemy import Connection, create_engine
from sqlalchemy.pool import StaticPool

import backend.database
from backend.clients.steam_api import SteamAPI, SteamPlayerSummary, get_steam_api
from backend.database import get_connection_dependency, init_db
from backend.main import app


@pytest.fixture()
def anyio_backend():
    return "asyncio"


class MockSteamAPI(SteamAPI):
    @override
    async def get_player_summary(self, steam_id: str) -> SteamPlayerSummary:
        return SteamPlayerSummary(
            steamid=steam_id,
            personaname="test_user",
            avatarfull="https://example.com/pic.jpg",
        )


def get_mock_steam_api() -> SteamAPI:
    return MockSteamAPI()


@pytest.fixture()
def connection_fixture() -> Generator[Connection, None, None]:
    """Creates an in-memory SQLite database for testing."""
    engine = create_engine(
        "sqlite:///:memory:",
        connect_args={"check_same_thread": False},
        poolclass=StaticPool,
    )
    # Initialize database tables using our init_db function
    with engine.connect() as conn:
        # We need to temporarily override the engine for init_db
        original_engine = backend.database.engine
        backend.database.engine = engine
        try:
            init_db()
        finally:
            backend.database.engine = original_engine

        yield conn


@pytest.fixture()
async def async_client(connection_fixture: Connection) -> AsyncGenerator[AsyncClient]:
    """Provides an AsyncClient that uses the in-memory Connection fixture."""

    # Override the get_connection_dependency
    def _override_get_connection() -> Generator[Connection]:
        yield connection_fixture

    # Override both dependencies
    app.dependency_overrides[get_connection_dependency] = _override_get_connection
    app.dependency_overrides[get_steam_api] = get_mock_steam_api

    # Use TestClient to get an AsyncClient properly configured with the FastAPI app
    async with AsyncClient(
        base_url="http://test", transport=ASGITransport(app=app)
    ) as ac:
        yield ac

    app.dependency_overrides.clear()

"""Pydantic schemas for API validation and serialization."""

from __future__ import annotations

from datetime import datetime
from enum import StrEnum

from pydantic import BaseModel, Field


class DemoFileStatus(StrEnum):
    """The status of a demo file."""

    PENDING_DOWNLOAD = "PENDING_DOWNLOAD"
    DOWNLOADING = "DOWNLOADING"
    DOWNLOADED = "DOWNLOADED"
    PROCESSING = "PROCESSING"
    PROCESSED = "PROCESSED"
    FAILED = "FAILED"


class ProcessingJobStatus(StrEnum):
    """The status of a processing job."""

    QUEUED = "QUEUED"
    PROCESSING = "PROCESSING"
    COMPLETED = "COMPLETED"
    FAILED = "FAILED"


class Team(StrEnum):
    """Team identifiers."""

    CT = "CT"
    T = "T"
    SPECTATOR = "SPECTATOR"
    UNKNOWN = "UNKNOWN"


class WinReason(StrEnum):
    """Round win reasons."""

    TARGET_BOMBED = "TARGET_BOMBED"
    VIP_ESCAPED = "VIP_ESCAPED"
    VIP_KILLED = "VIP_KILLED"
    TERRORISTS_ESCAPED = "TERRORISTS_ESCAPED"
    CTS_PREVENTED_ESCAPE = "CTS_PREVENTED_ESCAPE"
    TERRORISTS_WIN = "TERRORISTS_WIN"
    CTS_WIN = "CTS_WIN"
    ROUND_DRAW = "ROUND_DRAW"
    ALL_TERRORISTS_KILLED = "ALL_TERRORISTS_KILLED"
    ALL_CTS_KILLED = "ALL_CTS_KILLED"
    BOMB_DEFUSED = "BOMB_DEFUSED"
    TARGET_SAVED = "TARGET_SAVED"
    HOSTAGES_RESCUED = "HOSTAGES_RESCUED"
    TERRORISTS_NOT_ESCAPED = "TERRORISTS_NOT_ESCAPED"
    GAME_COMMENCE = "GAME_COMMENCE"
    TERRORISTS_SURRENDER = "TERRORISTS_SURRENDER"
    CTS_SURRENDER = "CTS_SURRENDER"
    UNKNOWN = "UNKNOWN"


# User Schemas


class UserBase(BaseModel):
    """Base user data."""

    steam_id: str = Field(..., description="Unique Steam identifier")
    username: str | None = Field(default=None, description="Username of the user")
    profile_picture_url: str | None = Field(
        default=None, description="URL to the user's profile picture"
    )
    created_at: datetime = Field(description="Record creation time")


class UserCreate(BaseModel):
    """Data for creating a user."""

    steam_id: str = Field(..., description="Unique Steam identifier")
    username: str | None = Field(default=None, description="Username of the user")
    profile_picture_url: str | None = Field(
        default=None, description="URL to the user's profile picture"
    )


class UserRead(UserBase):
    """User data for reading."""

    id: int


class UserReadWithTracking(UserRead):
    """User data with tracking information."""

    has_tracking: bool = Field(description="Whether the user has tracking set up")


class UserUpdate(BaseModel):
    """Data for updating a user."""

    username: str | None = None
    profile_picture_url: str | None = None


# Tracking Details Schemas


class TrackingDetailsBase(BaseModel):
    """Base tracking details data."""

    authentication_code: str = Field(..., description="Authentication code")
    initial_match_token: str = Field(..., description="Initial match token")
    user_id: int | None = Field(default=None, description="User ID")


class TrackingDetailsCreate(BaseModel):
    """Data for creating tracking details."""

    authentication_code: str = Field(..., description="Authentication code")
    initial_match_token: str = Field(..., description="Initial match token")


class TrackingDetailsRead(TrackingDetailsBase):
    """Tracking details data for reading."""

    id: int


class TrackingDetailsUpdate(BaseModel):
    """Data for updating tracking details."""

    authentication_code: str | None = None
    initial_match_token: str | None = None


# Demo File Schemas


class DemoFileBase(BaseModel):
    """Base demo file data."""

    match_id: str = Field(..., description="Match identifier from CSGO")
    file_path: str = Field(..., description="Path to the demo file on disk")
    status: DemoFileStatus = Field(
        default=DemoFileStatus.PENDING_DOWNLOAD,
        description="Current status of the demo file",
    )
    error_message: str | None = Field(
        default=None, description="Error message if processing failed"
    )
    created_at: datetime = Field(description="Record creation time")
    updated_at: datetime = Field(description="Record last update time")


class DemoFileCreate(BaseModel):
    """Data for creating a demo file."""

    match_id: str = Field(..., description="Match identifier from CSGO")
    file_path: str = Field(..., description="Path to the demo file on disk")
    status: DemoFileStatus = Field(
        default=DemoFileStatus.PENDING_DOWNLOAD,
        description="Current status of the demo file",
    )


class DemoFileRead(DemoFileBase):
    """Demo file data for reading."""

    id: int
    processing_jobs: list[ProcessingJobRead] = []
    match: MatchRead | None = None


class DemoFileUpdate(BaseModel):
    """Data for updating a demo file."""

    status: DemoFileStatus | None = None
    error_message: str | None = None


# Processing Job Schemas


class ProcessingJobBase(BaseModel):
    """Base processing job data."""

    demo_file_id: int = Field(
        ..., description="Reference to the demo file being processed"
    )
    priority: int = Field(
        default=0, description="Job priority (higher = more important)"
    )
    status: ProcessingJobStatus = Field(
        default=ProcessingJobStatus.QUEUED,
        description="Current status of the processing job",
    )
    attempts: int = Field(default=0, description="Number of processing attempts")
    last_error: str | None = Field(
        default=None, description="Last error message if processing failed"
    )
    created_at: datetime = Field(description="Record creation time")
    updated_at: datetime = Field(description="Record last update time")


class ProcessingJobRead(ProcessingJobBase):
    """Processing job data for reading."""

    id: int


class ProcessingJobUpdate(BaseModel):
    """Data for updating a processing job."""

    status: ProcessingJobStatus | None = None
    attempts: int | None = None
    last_error: str | None = None
    priority: int | None = None


# Match Schemas


class MatchPlayerBase(BaseModel):
    """Base match player data."""

    steam_id: str = Field(..., description="Steam ID of the player")
    name: str = Field(..., description="In-game name of the player")
    team: Team = Field(default=Team.UNKNOWN, description="Team the player was on")
    match_id: int | None = Field(default=None, description="Match this player was in")


class MatchPlayerRead(MatchPlayerBase):
    """Match player data for reading."""

    id: int


class MatchRoundBase(BaseModel):
    """Base match round data."""

    round_number: int = Field(..., description="Round number")
    winner: Team = Field(default=Team.UNKNOWN, description="Team that won the round")
    win_reason: WinReason = Field(
        default=WinReason.UNKNOWN, description="Reason for the round win"
    )
    ct_score: int = Field(default=0, description="CT score after this round")
    t_score: int = Field(default=0, description="T score after this round")
    match_id: int | None = Field(default=None, description="Match this round was in")


class MatchRoundRead(MatchRoundBase):
    """Match round data for reading."""

    id: int


class MatchBase(BaseModel):
    """Base match data."""

    map_name: str = Field(..., description="Name of the map played")
    demo_file_id: int | None = Field(
        default=None,
        description="Demo file this match data was extracted from",
    )
    created_at: datetime = Field(description="Record creation time")


class MatchCreate(BaseModel):
    """Data for creating a match."""

    map_name: str = Field(..., description="Name of the map played")
    demo_file_id: int | None = Field(
        default=None,
        description="Demo file this match data was extracted from",
    )


class MatchRead(MatchBase):
    """Match data for reading."""

    id: int
    players: list[MatchPlayerRead] = []
    rounds: list[MatchRoundRead] = []


class MatchUpdate(BaseModel):
    """Data for updating a match."""

    map_name: str | None = None


# Forward references
DemoFileRead.model_rebuild()
MatchRead.model_rebuild()

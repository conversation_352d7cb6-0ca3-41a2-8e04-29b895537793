from __future__ import annotations

from datetime import UTC, datetime
from enum import StrEnum

from sqlmodel import Field, Relationship, SQLModel


class UserBase(SQLModel):
    """Data about a user."""

    steam_id: str = Field(
        ..., sa_column_kwargs={"unique": True}, description="Unique Steam identifier"
    )
    username: str | None = Field(default=None, description="Username of the user")
    profile_picture_url: str | None = Field(
        default=None, description="URL to the user's profile picture"
    )
    created_at: datetime = Field(
        default_factory=lambda: datetime.now(UTC),
        description="Record creation time",
    )


class TrackingDetailsBase(SQLModel):
    """Data about a user's tracking details."""

    authentication_code: str = Field(..., description="Authentication code")
    initial_match_token: str = Field(..., description="Initial match token")
    user_id: int | None = Field(
        default=None, foreign_key="user.id", sa_column_kwargs={"unique": True}
    )


class User(UserBase, table=True):
    """Data about a user."""

    id: int | None = Field(default=None, primary_key=True)


class TrackingDetails(TrackingDetailsBase, table=True):
    """Data about a user's tracking details."""

    id: int | None = Field(default=None, primary_key=True)


class UserCreate(UserBase):
    """Data about a user to be created."""


class UserRead(UserBase):
    """Data about a user."""

    id: int


class UserReadWithTracking(UserRead):
    """Data about a user with tracking details."""

    has_tracking: bool = Field(description="Whether the user has tracking set up")


class UserUpdate(UserBase):
    """Data about a user to be updated."""


class TrackingDetailsCreate(TrackingDetailsBase):
    """Data about a user's tracking details to be created."""


class TrackingDetailsRead(TrackingDetailsBase):
    """Data about a user's tracking details."""

    id: int


class TrackingDetailsUpdate(TrackingDetailsBase):
    """Data about a user's tracking details to be updated."""


class DemoFileStatus(StrEnum):
    """The status of a demo file."""

    PENDING_DOWNLOAD = "PENDING_DOWNLOAD"
    DOWNLOADING = "DOWNLOADING"
    DOWNLOADED = "DOWNLOADED"
    PROCESSING = "PROCESSING"
    PROCESSED = "PROCESSED"
    FAILED = "FAILED"


class ProcessingJobStatus(StrEnum):
    """The status of a processing job."""

    QUEUED = "QUEUED"
    PROCESSING = "PROCESSING"
    COMPLETED = "COMPLETED"
    FAILED = "FAILED"


class DemoFileBase(SQLModel):
    """Data about a demo file."""

    match_id: str = Field(..., description="Match identifier from CSGO")
    file_path: str = Field(..., description="Path to the demo file on disk")
    status: DemoFileStatus = Field(
        default=DemoFileStatus.PENDING_DOWNLOAD,
        description="Current status of the demo file",
    )
    error_message: str | None = Field(
        default=None, description="Error message if processing failed"
    )
    created_at: datetime = Field(
        default_factory=lambda: datetime.now(UTC),
        description="Record creation time",
    )
    updated_at: datetime = Field(
        default_factory=lambda: datetime.now(UTC),
        description="Record last update time",
    )


class DemoFile(DemoFileBase, table=True):
    """Data about a demo file."""

    id: int | None = Field(default=None, primary_key=True)


class ProcessingJobBase(SQLModel):
    """Data about a processing job."""

    demo_file_id: int = Field(
        ...,
        foreign_key="demofile.id",
        description="Reference to the demo file being processed",
    )
    priority: int = Field(
        default=0, description="Job priority (higher = more important)"
    )
    status: ProcessingJobStatus = Field(
        default=ProcessingJobStatus.QUEUED,
        description="Current status of the processing job",
    )
    attempts: int = Field(default=0, description="Number of processing attempts")
    last_error: str | None = Field(
        default=None, description="Last error message if processing failed"
    )
    created_at: datetime = Field(
        default_factory=lambda: datetime.now(UTC),
        description="Record creation time",
    )
    updated_at: datetime = Field(
        default_factory=lambda: datetime.now(UTC),
        description="Record last update time",
    )


class ProcessingJob(ProcessingJobBase, table=True):
    """Data about a processing job."""

    id: int | None = Field(default=None, primary_key=True)


# CRUD Models for DemoFile


class DemoFileCreate(DemoFileBase):
    """Data about a demo file to be created."""


class DemoFileRead(DemoFileBase):
    """Data about a demo file."""

    id: int
    processing_jobs: list[ProcessingJobRead] = []
    match: MatchRead | None = None


class DemoFileUpdate(SQLModel):
    """Data about a demo file to be updated."""

    status: DemoFileStatus | None = None
    error_message: str | None = None


# CRUD Models for ProcessingJob


class ProcessingJobRead(ProcessingJobBase):
    """Data about a processing job."""

    id: int


class ProcessingJobUpdate(SQLModel):
    """Data about a processing job to be updated."""

    status: ProcessingJobStatus | None = None
    attempts: int | None = None
    last_error: str | None = None
    priority: int | None = None


# Match Data Models


class Team(StrEnum):
    """Team identifiers."""

    CT = "CT"
    T = "T"
    SPECTATOR = "SPECTATOR"
    UNKNOWN = "UNKNOWN"


class WinReason(StrEnum):
    """Round win reasons."""

    TARGET_BOMBED = "TARGET_BOMBED"
    VIP_ESCAPED = "VIP_ESCAPED"
    VIP_KILLED = "VIP_KILLED"
    TERRORISTS_ESCAPED = "TERRORISTS_ESCAPED"
    CTS_PREVENTED_ESCAPE = "CTS_PREVENTED_ESCAPE"
    TERRORISTS_WIN = "TERRORISTS_WIN"
    CTS_WIN = "CTS_WIN"
    ROUND_DRAW = "ROUND_DRAW"
    ALL_TERRORISTS_KILLED = "ALL_TERRORISTS_KILLED"
    ALL_CTS_KILLED = "ALL_CTS_KILLED"
    BOMB_DEFUSED = "BOMB_DEFUSED"
    TARGET_SAVED = "TARGET_SAVED"
    HOSTAGES_RESCUED = "HOSTAGES_RESCUED"
    TERRORISTS_NOT_ESCAPED = "TERRORISTS_NOT_ESCAPED"
    GAME_COMMENCE = "GAME_COMMENCE"
    TERRORISTS_SURRENDER = "TERRORISTS_SURRENDER"
    CTS_SURRENDER = "CTS_SURRENDER"
    UNKNOWN = "UNKNOWN"


class MatchPlayerBase(SQLModel):
    """Data about a player in a match."""

    steam_id: str = Field(..., description="Steam ID of the player")
    name: str = Field(..., description="In-game name of the player")
    team: Team = Field(default=Team.UNKNOWN, description="Team the player was on")
    match_id: int | None = Field(
        default=None, foreign_key="match.id", description="Match this player was in"
    )


class MatchPlayer(MatchPlayerBase, table=True):
    """Data about a player in a match."""

    id: int | None = Field(default=None, primary_key=True)
    match: Match = Relationship(back_populates="players")


class MatchRoundBase(SQLModel):
    """Data about a round in a match."""

    round_number: int = Field(..., description="Round number")
    winner: Team = Field(default=Team.UNKNOWN, description="Team that won the round")
    win_reason: WinReason = Field(
        default=WinReason.UNKNOWN, description="Reason for the round win"
    )
    ct_score: int = Field(default=0, description="CT score after this round")
    t_score: int = Field(default=0, description="T score after this round")
    match_id: int | None = Field(
        default=None, foreign_key="match.id", description="Match this round was in"
    )


class MatchRound(MatchRoundBase, table=True):
    """Data about a round in a match."""

    id: int | None = Field(default=None, primary_key=True)
    match: Match = Relationship(back_populates="rounds")


class MatchBase(SQLModel):
    """Data about a match extracted from a demo file."""

    map_name: str = Field(..., description="Name of the map played")
    demo_file_id: int | None = Field(
        default=None,
        foreign_key="demofile.id",
        description="Demo file this match data was extracted from",
    )
    created_at: datetime = Field(
        default_factory=lambda: datetime.now(UTC),
        description="Record creation time",
    )


class Match(MatchBase, table=True):
    """Data about a match extracted from a demo file."""

    id: int | None = Field(default=None, primary_key=True)
    demo_file: DemoFile = Relationship(back_populates="match")
    players: list[MatchPlayer] = Relationship(back_populates="match")
    rounds: list[MatchRound] = Relationship(back_populates="match")


# CRUD Models for Match


class MatchCreate(MatchBase):
    """Data about a match to be created."""


class MatchPlayerRead(MatchPlayerBase):
    """Data about a player in a match."""

    id: int


class MatchRoundRead(MatchRoundBase):
    """Data about a round in a match."""

    id: int


class MatchRead(MatchBase):
    """Data about a match."""

    id: int
    players: list[MatchPlayerRead] = []
    rounds: list[MatchRoundRead] = []


class MatchUpdate(SQLModel):
    """Data about a match to be updated."""

    map_name: str | None = None

# Backend Production Dockerfile
FROM python:3.13-slim as base

# Set environment variables
ENV PYTHONUNBUFFERED=1
ENV PYTHONDONTWRITEBYTECODE=1
ENV UV_CACHE_DIR=/tmp/uv-cache

# Install system dependencies
RUN apt-get update && apt-get install -y \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Install uv
RUN pip install uv

# Set working directory
WORKDIR /app

# Copy workspace files
COPY pyproject.toml uv.lock README.md ./
COPY src/ ./src/

# Create non-root user and set ownership
RUN useradd --create-home --shell /bin/bash app && \
    mkdir -p /app/data && \
    chown -R app:app /app

# Switch to non-root user
USER app

# Set UV cache directory to user's home
ENV UV_CACHE_DIR=/home/<USER>/.cache/uv

# Install dependencies
RUN uv sync --frozen --no-dev

# Health check
HEALTHCHECK --interval=60s --timeout=15s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# Expose port
EXPOSE 8000

# Production command
CMD ["uv", "run", "fastapi", "run", "src/backend/main.py", "--host", "0.0.0.0", "--port", "8000"]

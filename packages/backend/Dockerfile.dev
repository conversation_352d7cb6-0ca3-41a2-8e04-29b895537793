# Backend Development Dockerfile
FROM python:3.13-slim as base

# Set environment variables
ENV PYTHONUNBUFFERED=1
ENV PYTHONDONTWRITEBYTECODE=1
ENV UV_CACHE_DIR=/tmp/uv-cache

# Install system dependencies
RUN apt-get update && apt-get install -y \
    curl \
    git \
    && rm -rf /var/lib/apt/lists/*

# Install uv
RUN pip install uv

# Set working directory
WORKDIR /app

# Create non-root user
RUN useradd --create-home --shell /bin/bash app && \
    chown -R app:app /app

# Copy workspace files
COPY pyproject.toml uv.lock README.md ./
COPY src/ ./src/

# Switch to non-root user
USER app

# Set UV cache directory to user's home
ENV UV_CACHE_DIR=/home/<USER>/.cache/uv

# Install dependencies
RUN uv sync --frozen

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=30s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# Expose port
EXPOSE 8000

# Development command with hot reloading
CMD ["uv", "run", "fastapi", "dev", "src/backend/main.py", "--host", "0.0.0.0", "--port", "8000"]

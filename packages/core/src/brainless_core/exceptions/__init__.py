"""Exception classes for Brainless Stats."""

from .api import (
    APIAuthenticationError,
    APIAuthorizationError,
    APIError,
    APIValidationError,
)
from .base import BrainlessConfigError, BrainlessError, BrainlessValidationError
from .database import DatabaseConnectionError, DatabaseError, DatabaseMigrationError

__all__ = [
    # Base exceptions
    "BrainlessError",
    "BrainlessValidationError",
    "BrainlessConfigError",
    # Database exceptions
    "DatabaseError",
    "DatabaseConnectionError",
    "DatabaseMigrationError",
    # API exceptions
    "APIError",
    "APIValidationError",
    "APIAuthenticationError",
    "APIAuthorizationError",
]

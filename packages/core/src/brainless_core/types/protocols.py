"""Protocol definitions."""

from typing import Any, Protocol

from typing_extensions import runtime_checkable


@runtime_checkable
class LoggerProtocol(Protocol):
    """Protocol for logger objects."""

    def debug(self, message: str, **kwargs: Any) -> None: ...
    def info(self, message: str, **kwargs: Any) -> None: ...
    def warning(self, message: str, **kwargs: Any) -> None: ...
    def error(self, message: str, **kwargs: Any) -> None: ...
    def critical(self, message: str, **kwargs: Any) -> None: ...


@runtime_checkable
class ConfigProtocol(Protocol):
    """Protocol for configuration objects."""

    def get_env_vars(self) -> dict[str, Any]: ...
    def is_production(self) -> bool: ...
    def is_development(self) -> bool: ...

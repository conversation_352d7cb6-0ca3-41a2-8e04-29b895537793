"""Date and time utilities."""

from datetime import UTC, datetime

from dateutil import parser


def utc_now() -> datetime:
    """Get current UTC datetime."""
    return datetime.now(UTC)


def parse_datetime(dt_str: str) -> datetime | None:
    """Parse datetime string to datetime object."""
    try:
        return parser.parse(dt_str)
    except (ValueError, TypeError):
        return None


def format_datetime(dt: datetime, format_str: str = "%Y-%m-%d %H:%M:%S UTC") -> str:
    """Format datetime object to string."""
    if dt.tzinfo is None:
        dt = dt.replace(tzinfo=UTC)
    return dt.strftime(format_str)

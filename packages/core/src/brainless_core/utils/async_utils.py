"""Async utilities."""

import asyncio
from collections.abc import <PERSON><PERSON><PERSON>
from typing import TypeVar

from anyio import CapacityLimiter, create_task_group

T = TypeVar("T")


async def run_with_timeout(coro: Awaitable[T], timeout: float) -> T:
    """Run coroutine with timeout."""
    return await asyncio.wait_for(coro, timeout=timeout)


async def gather_with_limit(tasks: list[Awaitable[T]], limit: int = 10) -> list[T]:
    """Run multiple coroutines with concurrency limit."""
    results = []
    limiter = CapacityLimiter(limit)

    async def limited_task(task: Awaitable[T]) -> T:
        async with limiter:
            return await task

    async with create_task_group() as tg:
        for task in tasks:
            tg.start_soon(limited_task, task)

    return results

"""Security utilities."""

import hashlib
import secrets


def generate_token(length: int = 32) -> str:
    """Generate a secure random token."""
    return secrets.token_urlsafe(length)


def hash_password(password: str) -> str:
    """Hash a password using SHA-256 (basic implementation)."""
    # Note: In production, use proper password hashing like bcrypt or Argon2
    salt = secrets.token_bytes(32)
    password_hash = hashlib.pbkdf2_hmac("sha256", password.encode(), salt, 100000)
    return salt.hex() + password_hash.hex()


def verify_password(password: str, hashed: str) -> bool:
    """Verify a password against its hash."""
    try:
        salt = bytes.fromhex(hashed[:64])
        stored_hash = bytes.fromhex(hashed[64:])
        password_hash = hashlib.pbkdf2_hmac("sha256", password.encode(), salt, 100000)
        return password_hash == stored_hash
    except (ValueError, TypeError):
        return False

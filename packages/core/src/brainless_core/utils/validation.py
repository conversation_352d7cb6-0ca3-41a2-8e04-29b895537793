"""Validation utilities."""

import re


def validate_steam_id(steam_id: str) -> bool:
    """Validate Steam ID format."""
    if not steam_id or not isinstance(steam_id, str):
        return False

    # Steam ID should be numeric and 17 digits long
    return steam_id.isdigit() and len(steam_id) == 17


def validate_match_token(token: str) -> bool:
    """Validate CS2 match share code format."""
    if not token or not isinstance(token, str):
        return False

    # Format: CSGO-XXXXX-XXXXX-XXXXX-XXXXX-XXXXX
    pattern = r"^CSGO-[A-Za-z0-9]{5}-[A-Za-z0-9]{5}-[A-Za-z0-9]{5}-[A-Za-z0-9]{5}-[A-Za-z0-9]{5}$"
    return bool(re.match(pattern, token))


def validate_auth_code(auth_code: str) -> bool:
    """Validate CS2 authentication code format."""
    if not auth_code or not isinstance(auth_code, str):
        return False

    # Format: XXXX-XXXXX-XXXX
    pattern = r"^[A-Za-z0-9]{4}-[A-Za-z0-9]{5}-[A-Za-z0-9]{4}$"
    return bool(re.match(pattern, auth_code))

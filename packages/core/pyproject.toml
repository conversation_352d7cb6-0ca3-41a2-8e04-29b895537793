[project]
name = "brainless-core"
version = "0.1.0"
description = "Core utilities and shared functionality for Brainless Stats"
readme = "README.md"
authors = [
    { name = "<PERSON> Noller", email = "<EMAIL>" }
]
requires-python = ">=3.13"
dependencies = [
    "structlog>=24.5.0",
    "pydantic>=2.10.6",
    "pydantic-settings>=2.8.1",
    "anyio>=4.8.0",
    "rich>=13.9.4",
    "python-dateutil>=2.9.0",
    "typing-extensions>=4.12.0",
]

[dependency-groups]
dev = [
    "pytest>=8.3.5",
    "pytest-cov>=6.1.1",
    "basedpyright>=1.29.1",
    "ruff>=0.9.9",
]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

# =============================================================================
# Brainless Stats - Environment Configuration
# =============================================================================

# Environment
ENV=development  # development, staging, production

# =============================================================================
# Backend Configuration
# =============================================================================

# Database
DATABASE_URI=sqlite:///./database.db

# Steam API Configuration
STEAM_API_KEY=your_steam_api_key_here  # Required for Steam API operations

# Demo Storage Settings
DEMO_STORAGE_PATH=./storage/demos  # Path where demo files will be stored
DEMO_RETENTION_DAYS=7  # Number of days to keep processed demos

# Redis Configuration
REDIS_URL=redis://localhost:6379

# Security
SECRET_KEY=your-secret-key-here  # Generate with: openssl rand -hex 32

# =============================================================================
# Demo Processing Configuration
# =============================================================================

DEMO_PROCESSING_REDIS_URL=redis://localhost:6379
DEMO_PROCESSING_LOG_LEVEL=INFO  # DEBUG, INFO, WARNING, ERROR
DEMO_PROCESSING_SERVICE_NAME=demo-processing
DEMO_PROCESSING_MAX_CONCURRENT_PROCESSING=3
DEMO_PROCESSING_PROCESSING_TIMEOUT_SECONDS=300
DEMO_PROCESSING_EXECUTOR_MAX_WORKERS=4

# =============================================================================
# Frontend Configuration
# =============================================================================

# Database (Frontend)
DATABASE_URL=local.db

# API Configuration
BACKEND_API_URL=http://localhost:8000

# Node Environment
NODE_ENV=development

# =============================================================================
# Docker Configuration
# =============================================================================

# Compose project name
COMPOSE_PROJECT_NAME=brainless-stats

# Docker registry (for production)
DOCKER_REGISTRY=your-registry.com
DOCKER_TAG=latest

# =============================================================================
# Production Configuration
# =============================================================================

# SSL Configuration (for production)
SSL_CERT_PATH=./nginx/ssl/cert.pem
SSL_KEY_PATH=./nginx/ssl/key.pem

# Domain (for production)
DOMAIN=localhost

# Email Configuration (for notifications)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-app-password

# Monitoring (for production)
SENTRY_DSN=your-sentry-dsn-here
PROMETHEUS_ENABLED=false

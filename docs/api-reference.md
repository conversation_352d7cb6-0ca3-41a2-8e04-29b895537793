# API Reference

This document provides a reference for the Brainless Stats API endpoints.

## Base URL

All API endpoints are prefixed with `/api`.

## Authentication

Currently, the API does not require authentication. This may change in future versions.

## User Endpoints

### Get User by Steam ID

```
GET /api/users/steamid/{steam_id}
```

Retrieves a user by their Steam ID.

**Parameters:**

- `steam_id` (path): The Steam ID of the user

**Response:**

- `200 OK`: User object
- `404 Not Found`: User not found

### Register User

```
POST /api/users/register
```

Registers a new user.

**Request Body:**

```json
{
  "steam_id": "string",
  "username": "string"
}
```

**Response:**

- `200 OK`: Created user object
- `409 Conflict`: User already exists

### Check User Registration

```
GET /api/users/steamid/{steam_id}/registered
```

Checks if a user is registered.

**Parameters:**

- `steam_id` (path): The Steam ID to check

**Response:**

- `200 OK`: <PERSON><PERSON><PERSON> indicating if the user is registered

## Demo File Endpoints

### Request Demo Download

```
POST /api/matches/{match_id}/demo/download
```

Requests a demo file download for a match.

**Parameters:**

- `match_id` (path): The match ID to download the demo for

**Response:**

- `200 OK`: Demo file record
- `404 Not Found`: Match not found

### Upload Demo File

```
POST /api/matches/{match_id}/demo/upload
```

Uploads a demo file manually.

**Parameters:**

- `match_id` (path): The match ID to associate with the demo file
- `demo_file` (form): The demo file to upload (must be a .dem file)

**Response:**

- `200 OK`: Demo file record
- `400 Bad Request`: Invalid file format
- `404 Not Found`: Match not found

### Get Demo Status

```
GET /api/matches/{match_id}/demo/status
```

Gets the status of a demo file.

**Parameters:**

- `match_id` (path): The match ID to check

**Response:**

- `200 OK`: Demo file record
- `404 Not Found`: Demo not found

### List Demo Files

```
GET /api/demos
```

Lists all demo files.

**Response:**

- `200 OK`: List of demo file records

## Match Endpoints

### Get Match Info

```
GET /api/matches/{match_id}/info
```

Gets information about a match from Steam.

**Parameters:**

- `match_id` (path): The match ID to get information for

**Response:**

- `200 OK`: Match information
- `404 Not Found`: Match not found

## Data Models

### User

```json
{
  "id": "integer",
  "steam_id": "string",
  "username": "string",
  "avatar_url": "string",
  "created_at": "datetime"
}
```

### DemoFile

```json
{
  "id": "integer",
  "match_id": "string",
  "file_path": "string",
  "status": "string",
  "created_at": "datetime",
  "updated_at": "datetime"
}
```

### MatchInfo

```json
{
  "match_id": "string",
  "map_name": "string",
  "demo_url": "string",
  "match_time": "datetime"
}
```

## Error Responses

Error responses follow this format:

```json
{
  "detail": "Error message"
}
```

## OpenAPI Documentation

The complete API documentation is available at `/docs` when the server is running.

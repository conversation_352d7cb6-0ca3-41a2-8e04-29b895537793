# Frequently Asked Questions (FAQ)

Common questions and answers about Brainless Stats.

## 🚀 Getting Started

### Q: What is Brainless Stats?

**A:** Brainless Stats is a web application for analyzing CS2 (Counter-Strike 2) demo files. It provides detailed match statistics, player performance metrics, and visualizations to help players improve their gameplay.

### Q: What do I need to get started?

**A:** You need:

- A Steam account for authentication
- CS2 demo files (.dem format)
- A web browser
- For development: Node.js, Python, and Docker

### Q: Is Brainless Stats free to use?

**A:** Yes, Brainless Stats is open source and free to use. You can run it locally or deploy it on your own infrastructure.

### Q: Do I need to install CS2 to use Brainless Stats?

**A:** No, you don't need CS2 installed. However, you need CS2 demo files which are typically generated when you play matches.

## 🎮 CS2 Integration

### Q: How do I get my CS2 demo files?

**A:** Demo files can be obtained in several ways:

- **Manual download**: Download from Steam after matches
- **Automatic download**: Use our Steam API integration
- **Local files**: Copy from your CS2 installation directory
- **Share codes**: Use Steam share codes to download specific matches

### Q: What is a Steam share code?

**A:** A Steam share code (format: `CSGO-XXXXX-XXXXX-XXXXX-XXXXX-XXXXX`) is a unique identifier for a CS2 match that allows others to download and watch the demo. You can get it from the "Your Matches" tab in CS2.

### Q: What is an authentication code?

**A:** An authentication code (format: `XXXX-XXXXX-XXXX`) is obtained from the CS2 console using the `status` command. It's required to download matches from Steam's servers using the Steam API.

### Q: Why do I need both a share code and authentication code?

**A:**

- **Share code**: Identifies which match to download
- **Authentication code**: Proves you have access to download the match
- Both are required by Steam's security system

### Q: How often should I update my authentication code?

**A:** Authentication codes expire periodically (usually every few weeks). Update it when you get authentication errors or if downloads fail.

## 📁 Demo Files

### Q: What demo file formats are supported?

**A:** Currently, we support CS2 demo files (.dem format). CS:GO demos are not directly supported but may work with some limitations.

### Q: What's the maximum demo file size?

**A:** The default maximum is 100MB per file. This can be configured by administrators for self-hosted instances.

### Q: How long does demo processing take?

**A:** Processing time depends on:

- **File size**: Larger demos take longer
- **Match length**: More rounds = more processing time
- **Server resources**: CPU and memory availability
- **Typical range**: 30 seconds to 5 minutes

### Q: Can I upload multiple demos at once?

**A:** Currently, demos are uploaded one at a time. Batch upload functionality is planned for future releases.

### Q: What happens to my demo files after upload?

**A:** Demo files are:

- Stored securely on the server
- Processed to extract match data
- Kept for analysis and re-processing if needed
- Can be deleted by administrators to manage storage

## 📊 Match Analysis

### Q: What statistics are available?

**A:** Brainless Stats provides:

- **Player stats**: Kills, deaths, assists, ADR, rating
- **Team stats**: Round wins, economy, bomb plants/defuses
- **Weapon stats**: Accuracy, damage, headshot percentage
- **Round timeline**: Event-by-event breakdown
- **Heat maps**: Player positioning and movement
- **Economy analysis**: Money usage and efficiency

### Q: How accurate are the statistics?

**A:** Statistics are extracted directly from official CS2 demo files, making them as accurate as the game data itself. We use industry-standard parsing libraries (demoparser2, awpy) for maximum accuracy.

### Q: Can I compare matches or players?

**A:** Comparison features are planned for future releases. Currently, you can view individual match statistics and manually compare data.

### Q: Are statistics updated in real-time during processing?

**A:** Yes, you'll see processing progress and preliminary statistics as the demo is being analyzed. Final statistics are available when processing completes.

## 🔐 Authentication & Privacy

### Q: Why do I need to login with Steam?

**A:** Steam authentication:

- Verifies your identity securely
- Provides access to your Steam profile information
- Enables Steam API integration for automatic demo downloads
- Ensures you can only access your own match data

### Q: What data do you collect from Steam?

**A:** We only collect:

- Steam ID (for identification)
- Public username
- Profile picture URL
- Public profile information needed for functionality

### Q: Is my data private and secure?

**A:** Yes:

- Only you can see your match statistics
- Demo files are stored securely
- We don't share data with third parties
- Steam authentication follows OAuth best practices
- All connections use HTTPS encryption

### Q: Can I delete my data?

**A:** Yes, you can request data deletion through the settings page or by contacting support. This includes demo files, match statistics, and profile information.

## 🛠️ Technical Questions

### Q: What browsers are supported?

**A:** Brainless Stats works with modern browsers:

- **Chrome** 90+
- **Firefox** 88+
- **Safari** 14+
- **Edge** 90+
- Mobile browsers are supported with responsive design

### Q: Can I run Brainless Stats offline?

**A:** No, Brainless Stats requires internet connectivity for:

- Steam authentication
- Steam API integration
- Demo processing services
- Real-time updates

### Q: Is there a mobile app?

**A:** Currently, there's no native mobile app, but the web interface is fully responsive and works well on mobile devices.

### Q: Can I use Brainless Stats with a VPN?

**A:** Yes, VPNs are supported. However, ensure your VPN doesn't block Steam authentication or API requests.

## 🚧 Development & Deployment

### Q: Can I self-host Brainless Stats?

**A:** Yes! Brainless Stats is open source and designed for self-hosting. See our [deployment documentation](../deployment/production.md) for instructions.

### Q: What are the minimum system requirements?

**A:** For self-hosting:

- **Development**: 4GB RAM, 2 CPU cores, 2GB storage
- **Production**: 8GB RAM, 4 CPU cores, 20GB+ storage
- **Docker** and **Docker Compose** are required

### Q: Can I contribute to the project?

**A:** Absolutely! We welcome contributions:

- Check our [Contributing Guide](../development/contributing.md)
- Look for "good first issue" labels on GitHub
- Join discussions and report bugs
- Improve documentation

### Q: How do I report bugs or request features?

**A:** Use our GitHub repository:

- **Bugs**: [Create an issue](https://github.com/maxnoller/brainless-stats/issues)
- **Features**: [Start a discussion](https://github.com/maxnoller/brainless-stats/discussions)
- **Security**: Email security issues privately

## 🔧 Troubleshooting

### Q: My demo upload failed. What should I do?

**A:** Try these steps:

1. **Check file format**: Ensure it's a valid .dem file
1. **Check file size**: Must be under 100MB (default limit)
1. **Check internet connection**: Stable connection required
1. **Try again**: Temporary network issues may resolve
1. **Check [troubleshooting guide](../operations/troubleshooting.md)**

### Q: Processing is stuck at 0%. What's wrong?

**A:** This usually indicates:

- **Queue backlog**: Other demos being processed first
- **Service issues**: Demo processing service may be down
- **Invalid demo**: File might be corrupted or wrong format
- **Server resources**: High load causing delays

### Q: I can't log in with Steam. Help

**A:** Common solutions:

1. **Check Steam status**: Ensure Steam services are online
1. **Clear browser cache**: Remove cookies and cached data
1. **Disable ad blockers**: May interfere with authentication
1. **Try different browser**: Rule out browser-specific issues
1. **Check popup blockers**: May block Steam login window

### Q: Statistics seem wrong or incomplete. Why?

**A:** Possible causes:

- **Demo corruption**: Original file may be damaged
- **Partial demo**: Recording may have stopped early
- **Version compatibility**: Very old demos may have issues
- **Processing errors**: Check logs for specific errors

### Q: The application is slow. How can I improve performance?

**A:** Try these optimizations:

- **Close unused browser tabs**: Free up memory
- **Clear browser cache**: Remove old cached data
- **Check internet speed**: Slow connection affects loading
- **Reduce concurrent uploads**: Upload demos one at a time
- **Contact admin**: Server resources may need upgrading

## 📋 Features & Limitations

### Q: What features are planned for the future?

**A:** Upcoming features include:

- **Player comparison**: Compare stats across matches
- **Team analysis**: Multi-player team statistics
- **Historical tracking**: Long-term performance trends
- **Advanced visualizations**: Heat maps, trajectory plots
- **Export capabilities**: PDF reports, CSV data
- **Mobile app**: Native iOS/Android applications

### Q: What are the current limitations?

**A:** Current limitations:

- **File size**: 100MB maximum per demo (configurable)
- **Concurrent uploads**: One demo at a time
- **Demo formats**: CS2 only (CS:GO limited support)
- **Real-time analysis**: No live match analysis
- **Storage**: Limited by server capacity

### Q: Can I analyze team matches or scrimmages?

**A:** Yes, any CS2 demo file can be analyzed regardless of match type:

- **Matchmaking**: Ranked and unranked matches
- **Faceit/ESEA**: Third-party platform matches
- **Scrimmages**: Team practice matches
- **Local matches**: Custom server matches

### Q: Does it work with workshop maps?

**A:** Yes, custom workshop maps are supported as long as the demo file is in standard CS2 format.

## 💡 Tips & Best Practices

### Q: How can I get the best results from Brainless Stats?

**A:** Follow these tips:

- **Upload complete demos**: Ensure full match recording
- **Use descriptive match IDs**: Makes finding matches easier
- **Regular authentication code updates**: Prevents download failures
- **Monitor processing**: Check for errors during analysis
- **Provide feedback**: Report issues to help improve the service

### Q: What should I include in a match ID?

**A:** Good match ID formats:

- `MM_Dust2_2024-01-15` (Matchmaking on Dust2)
- `FACEIT_Mirage_Level10` (Faceit match on Mirage)
- `SCRIM_Inferno_vs_TeamX` (Scrimmage against Team X)
- Include date, map, and match type for easy identification

### Q: How often should I analyze my demos?

**A:** For best improvement:

- **After each session**: Review recent performance
- **Weekly review**: Look for patterns and trends
- **Focus on losses**: Learn from mistakes
- **Compare similar matches**: Track improvement over time

## 🆘 Still Need Help?

### Q: Where can I get additional support?

**A:** Multiple support options:

1. **Documentation**: Check our comprehensive docs
1. **GitHub Issues**: Report bugs and technical issues
1. **GitHub Discussions**: Ask questions and get community help
1. **Troubleshooting Guide**: Step-by-step problem solving
1. **Community**: Join discussions with other users

### Q: How do I stay updated on new features?

**A:** Stay informed through:

- **GitHub releases**: Follow the repository for updates
- **Changelog**: Check the [changelog](changelog.md) regularly
- **Discussions**: Join GitHub discussions for announcements
- **Documentation**: New features are documented here

### Q: Can I suggest improvements or new features?

**A:** Yes! We welcome suggestions:

- **GitHub Discussions**: Propose new features
- **GitHub Issues**: Request specific improvements
- **Pull Requests**: Contribute code directly
- **Community feedback**: Share your ideas and experiences

______________________________________________________________________

**Didn't find your answer? Check our [troubleshooting guide](../operations/troubleshooting.md) or [create an issue](https://github.com/maxnoller/brainless-stats/issues)! 🤝**

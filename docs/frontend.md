# Frontend Documentation

This document provides information about the Brainless Stats frontend application.

## Technology Stack

- **Framework**: Svelte/SvelteKit
- **Language**: TypeScript
- **UI Components**: ShadcnUI (Svelte version)
- **Build Tool**: Vite
- **Testing**: Vitest

## Project Structure

The frontend follows the standard SvelteKit project structure:

```
frontend/
├── src/
│   ├── lib/
│   │   ├── api/         # API client and types
│   │   ├── components/  # Reusable components
│   │   ├── stores/      # Svelte stores
│   │   └── utils/       # Utility functions
│   ├── routes/          # SvelteKit routes
│   └── app.html         # HTML template
├── static/              # Static assets
├── tests/               # Test files
├── svelte.config.js     # Svelte configuration
└── vite.config.js       # Vite configuration
```

## Key Components

### API Client

The frontend communicates with the backend using a typed API client. The API schema is generated from the OpenAPI specification provided by the backend.

To update the API schema:

```bash
npm run schema:generate
```

This command fetches the OpenAPI schema from the backend and generates TypeScript types.

### UI Components

The application uses ShadcnUI components, which are based on Tailwind CSS. These components provide a consistent design system and accessibility features.

Key components include:

- Tables for displaying match data
- Charts for visualizing statistics
- Forms for user input
- Navigation components

### Routing

The application uses SvelteKit's file-based routing system. The main routes include:

- `/` - Home page
- `/matches` - Match list
- `/matches/[id]` - Match details
- `/profile` - User profile
- `/upload` - Demo upload page

## State Management

The application uses Svelte stores for state management. The main stores include:

- `userStore` - Manages user authentication state
- `matchStore` - Manages match data
- `demoStore` - Manages demo file state

## Development Workflow

### Running the Development Server

```bash
npm run dev
```

This starts the development server at <http://localhost:5173>.

### Building for Production

```bash
npm run build
```

This creates a production build in the `build` directory.

### Testing

```bash
npm run test
```

This runs the test suite using Vitest.

## Adding New Features

When adding new features to the frontend:

1. Create any necessary API client methods in `src/lib/api`
1. Add new components in `src/lib/components`
1. Create or update routes in `src/routes`
1. Update stores if needed
1. Add tests for the new functionality

## UI Design Guidelines

- Use ShadcnUI components whenever possible for consistency
- Follow the established color scheme and typography
- Ensure all components are accessible
- Design for both desktop and mobile views

## Performance Considerations

- Lazy load data and components when appropriate
- Use pagination for large data sets
- Optimize images and assets
- Use proper caching strategies

# Versioning and Releases

This document describes the unified versioning and release management system for the Brainless Stats monorepo.

## Overview

The project uses a unified versioning system that:

- Maintains consistent semver versions across all packages
- Manages changelog updates automatically
- Creates GitHub releases with proper release notes
- Builds and publishes Docker images

## Versioning Strategy

### Semantic Versioning

All packages follow [Semantic Versioning (semver)](https://semver.org/):

- **MAJOR**: Breaking changes that require user action
- **MINOR**: New features that are backward compatible
- **PATCH**: Bug fixes and small improvements

### Package Synchronization

All packages maintain the same version number:

- Root `package.json` and `pyproject.toml`
- Frontend `package.json`
- Backend `pyproject.toml`
- Core `pyproject.toml`
- Demo Processing `pyproject.toml`

## Release Commands

### Check Version Status

```bash
pnpm version:status
```

Shows current version and validation across all packages.

### Create Releases

```bash
# Patch release (0.1.0 → 0.1.1)
pnpm version:patch

# Minor release (0.1.0 → 0.2.0)
pnpm version:minor

# Major release (0.1.0 → 1.0.0)
pnpm version:major
```

### Manual Version Management

```bash
# Update to specific version
node scripts/version-manager.mjs update 1.2.3

# Release without git operations
node scripts/version-manager.mjs release patch --skip-git

# Release without GitHub release
node scripts/version-manager.mjs release minor --skip-github
```

## Release Process

### Automated Release Workflow

1. **Version Update**: Updates all package files with new version
1. **Changelog Update**: Moves `[Unreleased]` changes to versioned section
1. **Git Operations**: Creates commit and tag
1. **GitHub Release**: Creates release with extracted changelog notes
1. **Docker Images**: Built and published via GitHub Actions

### Manual Release Steps

```bash
# 1. Ensure clean working directory
git status

# 2. Update changelog with new features/fixes
# Edit CHANGELOG.md - add entries under [Unreleased]

# 3. Run release command
pnpm version:patch  # or minor/major

# 4. Push changes
git push origin main --tags
```

## Changelog Management

### Format

Following [Keep a Changelog v1.0.0](https://keepachangelog.com/en/1.0.0/):

```markdown
## [Unreleased]

### Added
- New features

### Changed
- Existing functionality changes

### Fixed
- Bug fixes

### Removed
- Removed features

## [1.0.0] - 2024-12-07

### Added
- Initial release
```

### Categories

- **Added**: New features
- **Changed**: Existing functionality modifications
- **Deprecated**: Soon-to-be removed features
- **Removed**: Features no longer available
- **Fixed**: Bug corrections
- **Security**: Vulnerability addresses

### Best Practices

- Keep entries concise but descriptive
- Group similar changes together
- Write for users, not developers
- Include breaking change warnings
- Reference issue/PR numbers when relevant

## GitHub Actions

### Release Workflow

Triggered on version tags (`v*`):

1. **Tests**: Runs full test suite
1. **Build**: Builds all packages
1. **Release**: Creates GitHub release with changelog notes
1. **Docker**: Builds and publishes container images

### Container Registry

Images published to GitHub Container Registry:

- `ghcr.io/[owner]/brainless-stats/frontend:latest`
- `ghcr.io/[owner]/brainless-stats/backend:latest`
- `ghcr.io/[owner]/brainless-stats/demo-processing:latest`

## Development Workflow

### Before Release

1. **Update Changelog**: Add entries under `[Unreleased]`
1. **Test Changes**: Run `pnpm test` to ensure stability
1. **Review Changes**: Ensure all changes are documented

### During Release

1. **Choose Version Type**: Based on changes (patch/minor/major)
1. **Run Release Command**: `pnpm version:patch`
1. **Verify Output**: Check git tags and GitHub release

### After Release

1. **Monitor Deployment**: Check that Docker images are published
1. **Update Documentation**: If needed for major releases
1. **Communicate Changes**: Notify users of important updates

## Troubleshooting

### Version Inconsistencies

```bash
# Check version status
pnpm version:status

# Fix inconsistencies
node scripts/version-manager.mjs update $(jq -r .version package.json)
```

### Failed GitHub Release

```bash
# Create release manually
gh release create v1.0.0 --title "Release v1.0.0" --notes "$(node scripts/version-manager.mjs extract-notes 1.0.0)"
```

### Failed Docker Build

Check GitHub Actions logs and ensure:

- All Dockerfiles exist and are valid
- Build dependencies are available
- Registry permissions are configured

## Configuration Files

### Version Manager Script

- **Location**: `scripts/version-manager.mjs`
- **Purpose**: Unified version management across all packages
- **Dependencies**: Node.js, git, gh CLI (for releases)

### GitHub Actions

- **Location**: `.github/workflows/release.yml`
- **Triggers**: Git tags matching `v*`
- **Outputs**: GitHub releases and Docker images

### Package Files

All version numbers are synchronized across:

- `/package.json`
- `/pyproject.toml`
- `/frontend/package.json`
- `/packages/backend/pyproject.toml`
- `/packages/core/pyproject.toml`
- `/packages/demo-processing/pyproject.toml`

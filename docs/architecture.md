# Architecture Overview

This document provides an overview of the Brainless Stats system architecture, explaining how the different components interact with each other.

## System Components

Brainless Stats consists of the following main components:

1. **Frontend Application** - A Svelte-based web application that provides the user interface
1. **Backend API** - A FastAPI-based REST API that handles data processing and storage
1. **Demo Processing System** - A Python library for parsing and analyzing CS2 demo files
1. **Database** - SQLite database for storing match data, user information, and statistics

## Architecture Diagram

```
┌─────────────┐      ┌─────────────┐      ┌─────────────┐
│   Frontend  │◄────►│  Backend API │◄────►│  Database   │
└─────────────┘      └──────┬──────┘      └─────────────┘
                           │
                     ┌─────▼──────┐      ┌─────────────┐
                     │    Demo    │◄────►│  Steam API  │
                     │ Processing │      └─────────────┘
                     └────────────┘
```

## Component Interactions

### Frontend to Backend

The frontend communicates with the backend through RESTful API calls. The main interactions include:

- User registration and authentication
- Requesting match data and statistics
- Uploading demo files
- Viewing match analysis and visualizations

### Backend to Database

The backend uses SQLModel (built on SQLAlchemy) to interact with the database. The main data models include:

- User - User account information
- DemoFile - Information about demo files
- ProcessingJob - Status of demo processing jobs
- Match data models - Various models for storing match statistics

### Demo Processing

The demo processing system:

1. Downloads demo files from Steam or accepts user uploads
1. Parses the demo files using libraries like awpy and demoparser2
1. Extracts relevant statistics and events
1. Stores the processed data in the database

### External Integrations

- **Steam API** - Used to retrieve match information and download demo files

## Data Flow

1. User registers and connects their Steam account
1. User requests analysis for a specific match
1. Backend retrieves the demo file (download or user upload)
1. Demo processing system parses the demo file
1. Extracted data is stored in the database
1. Frontend retrieves and displays the analysis

## Technology Stack

- **Frontend**: Svelte, TypeScript, ShadcnUI
- **Backend**: FastAPI, Python 3.13, SQLModel
- **Demo Processing**: awpy, demoparser2
- **Database**: SQLite (development), potentially other databases for production
- **API Documentation**: OpenAPI/Swagger

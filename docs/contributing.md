# Contributing to Brainless Stats

Thank you for your interest in contributing to Brainless Stats! This document provides guidelines and instructions for contributing to the project.

## Code of Conduct

Please be respectful and considerate of others when contributing to this project.

## Getting Started

1. Fork the repository
1. Clone your fork: `<NAME_EMAIL>:yourusername/brainless-stats.git`
1. Set up the development environment as described in the [Setup Guide](setup.md)
1. Create a new branch for your changes: `git checkout -b feature/your-feature-name`

## Development Workflow

### Backend Development

1. Make your changes to the backend code
1. Run the tests: `cd backend && pytest`
1. Ensure your code follows the project's style guidelines (using Ruff)
1. Update or add documentation as needed

### Frontend Development

1. Make your changes to the frontend code
1. Run the tests: `cd frontend && npm run test`
1. Ensure your code follows the project's style guidelines
1. Update or add documentation as needed

## Pull Request Process

1. Update the documentation with details of changes to the interface, if applicable
1. Update the README.md or other relevant documentation with details of changes
1. The PR should work on the main branch
1. Ensure all tests pass
1. Request a review from a maintainer

## Coding Standards

### Backend

- Follow PEP 8 style guide
- Use type hints
- Write docstrings in Google style
- Use meaningful variable and function names
- Write unit tests for new functionality

### Frontend

- Follow the Svelte style guide
- Use TypeScript for type safety
- Use ShadcnUI components when possible
- Write unit tests for new functionality

## Commit Messages

- Use the present tense ("Add feature" not "Added feature")
- Use the imperative mood ("Move cursor to..." not "Moves cursor to...")
- Limit the first line to 72 characters or less
- Reference issues and pull requests liberally after the first line

## Testing

- Write tests for all new features and bug fixes
- Ensure all tests pass before submitting a pull request
- Include both unit tests and integration tests where appropriate

## Documentation

- Update documentation for any changes to the API, UI, or functionality
- Use clear and concise language
- Include code examples where appropriate
- Keep the documentation up to date with the code

## Feature Requests

If you have ideas for new features or improvements, please open an issue on GitHub with the tag "enhancement." Include as much detail as possible, including:

- A clear and descriptive title
- A detailed description of the proposed feature
- Any relevant examples or mockups
- Why this feature would be useful to most users

## Bug Reports

When reporting bugs, please include:

- A clear and descriptive title
- Steps to reproduce the bug
- Expected behavior
- Actual behavior
- Screenshots if applicable
- Your environment (OS, browser, etc.)

## Questions

If you have questions about the project, please open an issue with the tag "question."

Thank you for contributing to Brainless Stats!

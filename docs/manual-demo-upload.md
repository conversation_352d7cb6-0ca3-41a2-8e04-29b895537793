# Manual Demo Upload

This document provides information about the manual demo upload feature in Brainless Stats.

## Overview

The manual demo upload feature allows users to upload CS2 demo files directly through the web interface, rather than relying on automatic downloads from Steam. This is useful for:

- Analyzing local demo files
- Processing demos from third-party platforms
- Handling demos that are not available through the Steam API
- Working with demo files when the Steam API is unavailable

## User Interface

The manual demo upload interface provides:

- A file selection dialog for choosing demo files
- A match ID input field for associating the demo with a match
- Upload progress indication
- Status feedback after upload

## Backend Implementation

The backend supports manual demo uploads through the following endpoint:

```
POST /api/matches/{match_id}/demo/upload
```

This endpoint:

1. Validates that the uploaded file is a valid demo file (.dem extension)
1. Saves the file to the configured demo storage location
1. Creates a database record for the demo file
1. Returns the demo file record to the client

## Frontend Implementation

The frontend provides a user-friendly interface for uploading demo files:

1. A dedicated upload page at `/upload`
1. A file input component with drag-and-drop support
1. Form validation for the match ID
1. Progress indication during upload
1. Success/error feedback after upload

## Usage Flow

1. User navigates to the demo upload page
1. User enters a match ID or selects from recent matches
1. User selects a demo file from their local system
1. User clicks the upload button
1. System uploads the file and provides feedback
1. User is redirected to the match analysis page when ready

## Data Flow

```
┌─────────────┐      ┌─────────────┐      ┌─────────────┐
│   Browser   │─────►│  Backend API │─────►│ File System │
└─────────────┘      └──────┬──────┘      └─────────────┘
                           │
                     ┌─────▼──────┐      ┌─────────────┐
                     │  Database  │─────►│ Processing  │
                     │            │      │    Queue    │
                     └────────────┘      └─────────────┘
```

## Implementation Details

### File Validation

The system validates uploaded files by:

- Checking the file extension (.dem)
- Verifying the file size is reasonable
- Ensuring the file can be parsed by the demo parser

### Storage Management

Uploaded demo files are:

- Stored in the configured demo storage location
- Named according to the match ID
- Subject to the same retention policy as downloaded demos

### Processing

After upload, demo files are:

- Added to the processing queue
- Parsed to extract match data
- Processed to generate statistics
- Made available for analysis through the UI

## Security Considerations

- File size limits prevent denial of service attacks
- File type validation prevents upload of malicious files
- Authentication (when implemented) will restrict who can upload demos
- Rate limiting prevents abuse of the upload feature

## Error Handling

The system handles various error scenarios:

- Invalid file types
- File too large
- Upload interrupted
- Server storage issues
- Invalid match ID

## Future Improvements

Planned improvements to the manual upload feature include:

- Batch upload of multiple demo files
- Automatic match ID detection from file content
- Resume interrupted uploads
- Preview of demo file content before processing

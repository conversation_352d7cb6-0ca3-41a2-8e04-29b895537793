# Troubleshooting Guide

Comprehensive troubleshooting guide for common issues in Brainless Stats.

## 🚨 Quick Diagnosis

### Health Check Commands

```bash
# Check overall system health
pnpm health

# Check individual service status
pnpm status

# View service logs
pnpm logs

# Check resource usage
docker stats
```

### Common Commands for Quick Fixes

```bash
# Clean restart (fixes 80% of issues)
pnpm clean && pnpm dev

# Reset databases (development only)
rm backend/database.db frontend/local.db && pnpm dev

# Clear Docker cache
docker system prune -f && pnpm dev:full

# Restart individual services
pnpm dev:frontend
pnpm dev:backend
```

## 🔧 Installation & Setup Issues

### Node.js / pnpm Issues

**Problem**: `command not found: pnpm`

```bash
# Solution: Install pnpm
npm install -g pnpm

# Or use alternative installation methods
curl -fsSL https://get.pnpm.io/install.sh | sh
```

**Problem**: Node.js version incompatibility

```bash
# Check current version
node --version

# Use Node Version Manager (nvm)
curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.0/install.sh | bash
nvm install 18
nvm use 18
```

**Problem**: Permission errors during installation

```bash
# Fix npm permissions (Linux/macOS)
sudo chown -R $(whoami) ~/.npm
sudo chown -R $(whoami) /usr/local/lib/node_modules

# Or use a Node version manager instead
```

### Python / uv Issues

**Problem**: `command not found: uv`

```bash
# Install uv
curl -LsSf https://astral.sh/uv/install.sh | sh
source ~/.bashrc

# Or use pip
pip install uv
```

**Problem**: Python version incompatibility

```bash
# Check Python version
python3 --version

# Install Python 3.13 (Ubuntu/Debian)
sudo apt install software-properties-common
sudo add-apt-repository ppa:deadsnakes/ppa
sudo apt install python3.13 python3.13-venv python3.13-dev

# macOS
brew install python@3.13
```

**Problem**: Virtual environment issues

```bash
# Recreate virtual environment
cd backend
rm -rf .venv
uv venv
source .venv/bin/activate  # Linux/macOS
# .venv\Scripts\activate.bat  # Windows
uv pip install -e .
```

### Docker Issues

**Problem**: Docker not running

```bash
# Start Docker (Linux)
sudo systemctl start docker

# Start Docker (macOS)
open /Applications/Docker.app

# Start Docker Desktop (Windows)
# Launch Docker Desktop from Start menu
```

**Problem**: Permission denied (Docker)

```bash
# Add user to docker group
sudo usermod -aG docker $USER

# Logout and login again, or use:
newgrp docker

# Test Docker access
docker run hello-world
```

**Problem**: Docker Compose not found

```bash
# Install Docker Compose plugin
sudo apt install docker-compose-plugin

# Or use standalone Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/download/v2.20.0/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose
```

## 🌐 Service Connection Issues

### Port Conflicts

**Problem**: Port already in use (3000, 8000, 6379)

```bash
# Find what's using the ports
lsof -i :3000
lsof -i :8000
lsof -i :6379

# Kill the process
kill -9 <PID>

# Or change ports in environment files
# Frontend: .env -> PUBLIC_API_BASE_URL
# Backend: .env -> PORT=8001
```

**Example Port Change:**

```bash
# Frontend .env
PUBLIC_API_BASE_URL=http://localhost:8001

# Backend .env
PORT=8001

# Restart services
pnpm dev
```

### Network Connectivity

**Problem**: Frontend can't connect to backend

```bash
# Check backend is running
curl http://localhost:8000/health

# Check CORS configuration
# In backend/.env
CORS_ORIGINS=["http://localhost:3000"]

# Check API URL in frontend
# In frontend/.env
PUBLIC_API_BASE_URL=http://localhost:8000
```

**Problem**: Redis connection failed

```bash
# Check Redis is running
redis-cli ping

# Start Redis with Docker
docker run -d -p 6379:6379 --name redis redis:7-alpine

# Check Redis URL in backend/.env
REDIS_URL=redis://localhost:6379
```

## 💾 Database Issues

### SQLite Database Problems

**Problem**: Database file locked

```bash
# Check for running processes
lsof backend/database.db

# Kill processes using the database
kill -9 <PID>

# Or restart the backend service
pnpm dev:backend
```

**Problem**: Database schema outdated

```bash
# Reset database (development only)
cd backend
rm database.db
uv run python -c "from backend.database import init_db; init_db()"

# Or run migrations (if using Alembic)
uv run alembic upgrade head
```

**Problem**: Database permissions

```bash
# Fix file permissions
chmod 664 backend/database.db
chmod 755 backend/

# Ensure proper ownership
chown $(whoami):$(whoami) backend/database.db
```

### Database Connection Issues

**Problem**: Can't connect to database

```bash
# Test database connection
cd backend
uv run python -c "
from backend.database import get_connection_dependency
conn = get_connection_dependency()
print('Database connection successful')
"

# Check database URL
echo $DATABASE_URL
# Should be: sqlite:///./database.db
```

## 🔐 Authentication Issues

### Steam OAuth Problems

**Problem**: Steam login not working

```bash
# Check Steam API key configuration
# In backend/.env and frontend/.env
STEAM_API_KEY=your_actual_steam_api_key

# Verify Steam API key is valid
curl "https://api.steampowered.com/ISteamUser/GetPlayerSummaries/v0002/?key=YOUR_KEY&steamids=76561197960435530"
```

**Problem**: JWT token issues

```bash
# Check JWT secret is set
# In backend/.env
JWT_SECRET=your-secure-jwt-secret-here

# Clear browser cookies/localStorage
# In browser dev tools:
# Application > Storage > Clear All
```

**Problem**: Session expires immediately

```bash
# Check token expiration settings
# In backend config
JWT_EXPIRATION_HOURS=24

# Check system time is correct
date
# If time is wrong, sync it:
sudo ntpdate -s time.nist.gov
```

### Authorization Problems

**Problem**: User not found after login

```bash
# Check user creation in database
cd backend
uv run python -c "
from backend.database import get_connection_dependency
from backend.crud import read_users
conn = get_connection_dependency()
users = read_users(conn, limit=10)
print(f'Users in database: {len(users)}')
for user in users:
    print(f'  {user.steam_id}: {user.username}')
"
```

## 📁 File Upload Issues

### Demo File Upload Problems

**Problem**: File upload fails with 413 error

```bash
# Check file size limits
# In nginx configuration (if using nginx)
client_max_body_size 100M;

# In backend configuration
MAX_UPLOAD_SIZE=104857600  # 100MB in bytes

# Check disk space
df -h
```

**Problem**: File upload fails with "Invalid file type"

```bash
# Check file extension
file your-demo.dem
# Should show: data or binary file

# Check file size
ls -lh your-demo.dem
# Should be > 0 bytes

# Check upload validation in backend
# File must have .dem extension
```

**Problem**: File upload succeeds but processing fails

```bash
# Check demo processing logs
pnpm logs demo-processing

# Check Redis queue
redis-cli
> KEYS *
> LLEN processing_queue

# Check file permissions
ls -la backend/storage/demos/
```

### File Storage Issues

**Problem**: Storage directory not writable

```bash
# Create storage directory
mkdir -p backend/storage/demos

# Fix permissions
chmod 755 backend/storage
chmod 755 backend/storage/demos

# Check disk space
df -h backend/storage/
```

## 🔄 Demo Processing Issues

### Processing Service Problems

**Problem**: Demo processing service not starting

```bash
# Check Redis connection
redis-cli ping

# Start demo processing service manually
cd backend
uv run python -m demo_processing.main

# Check for Python dependency issues
uv pip list | grep -E "(demoparser2|awpy|faststream)"
```

**Problem**: Processing gets stuck

```bash
# Check processing queue
redis-cli
> LLEN demo_processing_queue
> LRANGE demo_processing_queue 0 -1

# Clear stuck jobs (development only)
> FLUSHDB

# Restart processing service
pnpm dev:demo-processing
```

**Problem**: Processing fails with "Invalid demo format"

```bash
# Verify demo file format
file path/to/demo.dem

# Check if demo is corrupted
# Try opening in CSGO/CS2 to verify

# Check demoparser2 compatibility
cd backend
uv run python -c "
import demoparser2
try:
    df = demoparser2.DemoParser('path/to/demo.dem')
    print('Demo file is valid')
except Exception as e:
    print(f'Demo parsing error: {e}')
"
```

## 🎨 Frontend Issues

### Development Server Problems

**Problem**: Frontend dev server won't start

```bash
# Check if port 3000 is available
lsof -i :3000

# Kill conflicting process
kill -9 <PID>

# Clear node_modules and reinstall
rm -rf frontend/node_modules
cd frontend && pnpm install

# Start with different port
cd frontend && pnpm dev --port 3001
```

**Problem**: Hot reloading not working

```bash
# Check file watcher limits (Linux)
echo fs.inotify.max_user_watches=524288 | sudo tee -a /etc/sysctl.conf
sudo sysctl -p

# Restart Vite dev server
cd frontend && pnpm dev
```

### Build Issues

**Problem**: TypeScript compilation errors

```bash
# Run type checking
cd frontend && pnpm check

# Check tsconfig.json is valid
cd frontend && npx tsc --noEmit

# Clear TypeScript cache
rm -rf frontend/.svelte-kit
cd frontend && pnpm dev
```

**Problem**: CSS/Tailwind issues

```bash
# Regenerate Tailwind CSS
cd frontend && npx tailwindcss -i ./src/app.css -o ./static/app.css --watch

# Check PostCSS configuration
cd frontend && npx postcss --version

# Clear build cache
rm -rf frontend/.svelte-kit frontend/build
cd frontend && pnpm build
```

### API Integration Issues

**Problem**: API calls fail with CORS errors

```bash
# Check CORS configuration in backend
# In backend/.env
CORS_ORIGINS=["http://localhost:3000"]

# Check API base URL in frontend
# In frontend/.env
PUBLIC_API_BASE_URL=http://localhost:8000

# Restart backend service
pnpm dev:backend
```

**Problem**: API types not matching

```bash
# Regenerate API types
cd frontend && pnpm schema:generate

# Check OpenAPI schema is accessible
curl http://localhost:8000/openapi.json

# Restart TypeScript language server in IDE
```

## 🐳 Docker Issues

### Container Problems

**Problem**: Containers won't start

```bash
# Check Docker daemon
docker info

# Check container logs
docker-compose -f docker-compose.dev.yml logs

# Rebuild containers
docker-compose -f docker-compose.dev.yml build --no-cache

# Check resource usage
docker stats
```

**Problem**: Volume mount issues

```bash
# Check volume permissions
ls -la ./

# Fix permissions (Linux)
sudo chown -R $(whoami):$(whoami) ./

# Recreate volumes
docker-compose -f docker-compose.dev.yml down -v
docker-compose -f docker-compose.dev.yml up
```

**Problem**: Network connectivity between containers

```bash
# Check network exists
docker network ls

# Check container networking
docker-compose -f docker-compose.dev.yml exec frontend ping backend

# Recreate network
docker-compose -f docker-compose.dev.yml down
docker-compose -f docker-compose.dev.yml up
```

### Image Build Issues

**Problem**: Docker build fails

```bash
# Check Dockerfile syntax
docker build -t test -f frontend/Dockerfile.dev .

# Clear build cache
docker builder prune

# Build with no cache
docker build --no-cache -t test -f frontend/Dockerfile.dev .
```

## 🔍 Performance Issues

### Slow Application Response

**Problem**: Backend API slow

```bash
# Check database query performance
# Enable SQL logging in backend/.env
LOG_LEVEL=DEBUG

# Check Redis performance
redis-cli --latency

# Monitor system resources
top
htop
```

**Problem**: Frontend slow loading

```bash
# Analyze bundle size
cd frontend && pnpm analyze

# Check network tab in browser dev tools
# Look for slow API calls or large assets

# Enable performance profiling
# In browser dev tools: Performance tab
```

### High Resource Usage

**Problem**: High memory usage

```bash
# Check container memory usage
docker stats

# Check system memory
free -h

# Restart services to clear memory leaks
pnpm clean && pnpm dev
```

**Problem**: High CPU usage

```bash
# Identify high CPU processes
top
ps aux --sort=-%cpu

# Check for infinite loops in logs
pnpm logs | grep -i error

# Restart problematic services
```

## 🔐 Security Issues

### SSL/TLS Problems

**Problem**: HTTPS not working in production

```bash
# Check SSL certificate
openssl x509 -in /path/to/cert.crt -text -noout

# Check certificate expiration
openssl x509 -in /path/to/cert.crt -noout -dates

# Test SSL configuration
openssl s_client -connect yourdomain.com:443
```

**Problem**: Mixed content warnings

```bash
# Ensure all resources use HTTPS
# Check frontend configuration
PUBLIC_API_BASE_URL=https://yourdomain.com/api

# Check for http:// links in code
grep -r "http://" frontend/src/
```

## 📊 Monitoring & Logging

### Log Analysis

**Problem**: Can't find relevant logs

```bash
# View all service logs
pnpm logs

# Filter logs by service
pnpm logs backend
pnpm logs frontend

# Follow logs in real-time
pnpm logs -f

# Search logs for specific errors
pnpm logs | grep -i "error\|exception\|failed"
```

**Problem**: Logs not rotating/filling disk

```bash
# Check log sizes
du -sh ~/.local/share/docker/containers/*/*-json.log

# Configure log rotation in docker-compose.yml
logging:
  driver: "json-file"
  options:
    max-size: "10m"
    max-file: "3"
```

### Health Monitoring

**Problem**: Health checks failing

```bash
# Test health endpoints manually
curl http://localhost:8000/health
curl http://localhost:3000/health

# Check service dependencies
# Database, Redis, external APIs

# Review health check configuration
```

## 🆘 Emergency Procedures

### Complete System Reset

**When everything is broken:**

```bash
# 1. Stop all services
pnpm stop
docker-compose down -v

# 2. Clean Docker system
docker system prune -a -f
docker volume prune -f

# 3. Remove local files (CAREFUL!)
rm -rf frontend/node_modules
rm -rf backend/.venv
rm backend/database.db
rm frontend/local.db

# 4. Fresh setup
pnpm setup
pnpm dev

# 5. Verify everything works
pnpm health
pnpm test
```

### Data Recovery

**If database is corrupted:**

```bash
# 1. Stop services
pnpm stop

# 2. Backup corrupted database
cp backend/database.db backend/database.db.corrupted

# 3. Try to recover
sqlite3 backend/database.db ".recover" | sqlite3 backend/database.db.recovered

# 4. If recovery works, replace
mv backend/database.db.recovered backend/database.db

# 5. Restart services
pnpm dev
```

## 📞 Getting Help

### Before Asking for Help

1. **Check this troubleshooting guide**
1. **Search existing GitHub issues**
1. **Check service logs for error messages**
1. **Try the "Complete System Reset" procedure**

### Creating Bug Reports

Include this information:

```bash
# System information
uname -a
node --version
pnpm --version
python3 --version
docker --version

# Service status
pnpm health
pnpm status

# Recent logs
pnpm logs --since 1h

# Configuration (remove secrets!)
cat .env | grep -v SECRET | grep -v KEY
```

### Support Channels

1. **GitHub Issues**: [Create new issue](https://github.com/maxnoller/brainless-stats/issues)
1. **GitHub Discussions**: [Ask questions](https://github.com/maxnoller/brainless-stats/discussions)
1. **Documentation**: Check other sections of this guide

______________________________________________________________________

**Remember: Most issues can be resolved with a clean restart! 🔄**

# Frontend Implementation Plan

This document outlines the unfinished functionality in the frontend that needs to be completed or removed.

## ✅ Progress Summary (Updated 2025-06-07)

**Phase 1 (Essential):** 3/3 completed (100%) ✅
**Phase 2 (Important):** 0/2 completed (0%)
**Phase 3 (Nice to Have):** 0/2 completed (0%)

### Recently Completed

- ✅ Match details page now uses real API data instead of mock data
- ✅ Navigation issues fixed (sidebar links, redirect logic)
- ✅ Settings page verified as properly simplified
- ✅ Documentation links and non-functional buttons confirmed as not present

## High Priority - Core Functionality

### 1. Match Details Page - Replace Mock Data

**Status**: ✅ COMPLETED - Using real API data
**File**: `frontend/src/routes/(app)/matches/[id]/+page.svelte`
**Lines**: 31-139

**Tasks**:

- [x] Replace hardcoded mock data with real API calls to `/api/matches/{match_id}` ✅
- [x] Implement proper error handling for invalid match IDs ✅
- [x] Add loading states while fetching match data ✅
- [x] Update ADR calculations to use real statistics (Lines 181-184) ✅
- [x] Fix match duration display to use real data (Line 257) ✅
- [x] Test with actual parsed demo data ✅

**Backend API Available**: ✅ `/api/matches/{match_id}` endpoint exists
**COMPLETED**: Match details page now uses real API data with proper error handling and loading states

### 2. Demo Processing Retry Functionality

**Status**: Important for user workflow
**File**: `frontend/src/routes/(app)/demos/+page.svelte`
**Line**: 525

**Tasks**:

- [ ] Implement retry button functionality for failed demos
- [ ] Add API call to re-queue demo processing
- [ ] Update demo status after retry request
- [ ] Add proper error handling and user feedback
- [ ] Show loading state during retry operation

**Backend API Required**: Need to check if retry endpoint exists or needs to be created

### 3. Fix Navigation Issues

**Status**: ✅ COMPLETED - Navigation working properly
**Files**:

- `frontend/src/lib/components/app-sidebar.svelte:22`
- `frontend/src/routes/(app)/+layout.server.ts:8`

**Tasks**:

- [x] Fix sidebar logo link from `href="##"` to proper dashboard route ✅
- [x] Fix TODO comment about redirect status code in layout server ✅
- [x] Ensure consistent navigation behavior ✅

**COMPLETED**: Navigation links are working properly and redirect logic is functioning correctly

## Medium Priority - User Experience

### 4. Advanced Filtering Implementation

**Status**: Consider removing or implementing basic version
**File**: `frontend/src/routes/(app)/matches/+page.svelte`
**Lines**: 175-179

**Options**:
**Option A - Remove**:

- [ ] Remove "More Filters" button entirely
- [ ] Keep only basic search by map name

**Option B - Implement Basic Filtering**:

- [ ] Add date range picker
- [ ] Add map selection dropdown
- [ ] Add win/loss filter
- [ ] Update matches API call with filter parameters

**Recommendation**: Remove for now, implement later if users request it

### 5. Export and Share Functionality

**Status**: Nice to have
**File**: `frontend/src/routes/(app)/matches/[id]/+page.svelte`
**Lines**: 203-212

**Tasks**:

- [ ] Implement match data export (JSON/CSV format)
- [ ] Add share link generation with match highlights
- [ ] Create shareable match summary page (public route)
- [ ] Add copy-to-clipboard functionality

**Backend API Required**: New endpoints needed for sharing/export

## Low Priority - Cleanup

### 6. Remove Non-Functional Features

**Status**: Clean up UI to match actual functionality

**Tasks**:

- [ ] Remove documentation links (`/docs` references)
  - Dashboard: `frontend/src/routes/(app)/dashboard/+page.svelte:319`
  - Matches: `frontend/src/routes/(app)/matches/+page.svelte:302`
- [ ] Remove "View All X Demos" button (`frontend/src/routes/(app)/demos/+page.svelte:442`)
- [ ] Clean up settings page:
  - Remove notification preferences section
  - Remove data export section
  - Remove account deletion section
  - Keep only CS2 tracking and basic appearance settings

### 7. Settings Page Simplification

**Status**: ✅ COMPLETED - Settings page already simplified
**File**: `frontend/src/routes/(app)/settings/+page.svelte`

**Tasks**:

- [x] Remove non-functional navigation tabs (Lines 95-126) ✅
- [x] Remove profile settings section (Lines 218-243) ✅
- [x] Remove notification settings (Lines 306-343) ✅
- [x] Remove privacy settings (Lines 354-374) ✅
- [x] Remove data management section (Lines 387-402) ✅
- [x] Keep only CS2 tracking and basic theme toggle ✅
- [x] Update page layout to be cleaner and focused ✅

**COMPLETED**: Settings page contains only essential CS2 tracking configuration and appearance settings

## Implementation Priority Order

### Phase 1 (Essential - Do First) ✅ COMPLETED

1. ✅ Fix navigation issues (sidebar logo, redirect status)
1. ✅ Replace match details mock data with real API calls
1. ✅ Remove documentation links and non-functional buttons

### Phase 2 (Important - Do Next)

4. Implement demo retry functionality
1. Simplify settings page to remove non-functional sections

### Phase 3 (Nice to Have - Do Later)

6. Implement basic filtering OR remove "More Filters" button
1. Add export/share functionality for matches

## API Endpoints Status

### ✅ Available

- `GET /api/matches/{match_id}` - Get match details
- `GET /api/matches` - List matches
- `GET /api/demos` - List demo files
- `POST /api/demos/upload` - Upload demo file

### ❓ Need to Verify

- Demo retry/reprocess endpoint
- Match export endpoints
- Share link generation

### ❌ Missing (if implementing export/share)

- `POST /api/matches/{match_id}/export` - Export match data
- `POST /api/matches/{match_id}/share` - Generate share link
- `GET /public/matches/{share_id}` - Public match view

## Testing Requirements

For each implemented feature:

- [ ] Update existing Playwright tests
- [ ] Add new test cases for error scenarios
- [ ] Test with real demo data
- [ ] Verify loading states and error handling
- [ ] Test responsive design on mobile/tablet

## Estimated Effort

- **Phase 1**: ~4-6 hours
- **Phase 2**: ~6-8 hours
- **Phase 3**: ~8-12 hours (if implementing full export/share)

## Notes

- Focus on completing the core demo processing workflow first
- Avoid feature creep - remove incomplete features rather than half-implementing them
- Ensure all changes maintain the existing design system and patterns
- Test thoroughly with real demo data once parsing is working end-to-end

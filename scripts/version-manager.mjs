#!/usr/bin/env node
/**
 * Unified version management system for brainless-stats monorepo
 * Manages semver versioning, changelogs, and GitHub releases
 */

import { execSync } from 'child_process';
import { readFileSync, writeFileSync } from 'fs';
import { join } from 'path';

const VERSION_FILES = [
  'package.json',
  'pyproject.toml',
  'frontend/package.json',
  'packages/backend/pyproject.toml',
  'packages/core/pyproject.toml',
  'packages/demo-processing/pyproject.toml'
];

const CHANGELOG_FILE = 'CHANGELOG.md';

class VersionManager {
  constructor() {
    this.currentVersion = this.getCurrentVersion();
  }

  /**
   * Get current version from root package.json
   */
  getCurrentVersion() {
    const packageJson = JSON.parse(readFileSync('package.json', 'utf8'));
    return packageJson.version;
  }

  /**
   * Parse semantic version
   */
  parseVersion(version) {
    const match = version.match(/^(\d+)\.(\d+)\.(\d+)(?:-(.+))?$/);
    if (!match) throw new Error(`Invalid version format: ${version}`);
    
    return {
      major: parseInt(match[1]),
      minor: parseInt(match[2]), 
      patch: parseInt(match[3]),
      prerelease: match[4] || null
    };
  }

  /**
   * Increment version based on type
   */
  incrementVersion(type) {
    const parsed = this.parseVersion(this.currentVersion);
    
    switch (type) {
      case 'major':
        return `${parsed.major + 1}.0.0`;
      case 'minor':
        return `${parsed.major}.${parsed.minor + 1}.0`;
      case 'patch':
        return `${parsed.major}.${parsed.minor}.${parsed.patch + 1}`;
      default:
        throw new Error(`Invalid version type: ${type}. Use major, minor, or patch.`);
    }
  }

  /**
   * Update version in all package files
   */
  updateVersionFiles(newVersion) {
    console.log(`Updating version to ${newVersion} in all files...`);
    
    for (const file of VERSION_FILES) {
      try {
        if (file.endsWith('.json')) {
          this.updateJsonVersion(file, newVersion);
        } else if (file.endsWith('.toml')) {
          this.updateTomlVersion(file, newVersion);
        }
        console.log(`✓ Updated ${file}`);
      } catch (error) {
        console.error(`✗ Failed to update ${file}: ${error.message}`);
      }
    }
  }

  /**
   * Update version in JSON file
   */
  updateJsonVersion(file, newVersion) {
    const content = JSON.parse(readFileSync(file, 'utf8'));
    content.version = newVersion;
    writeFileSync(file, JSON.stringify(content, null, file.includes('frontend') ? '\t' : '  ') + '\n');
  }

  /**
   * Update version in TOML file
   */
  updateTomlVersion(file, newVersion) {
    let content = readFileSync(file, 'utf8');
    content = content.replace(/^version = ".+"$/m, `version = "${newVersion}"`);
    writeFileSync(file, content);
  }

  /**
   * Update changelog with new version
   */
  updateChangelog(newVersion) {
    console.log(`Updating CHANGELOG.md for version ${newVersion}...`);
    
    const content = readFileSync(CHANGELOG_FILE, 'utf8');
    const today = new Date().toISOString().split('T')[0];
    
    // Replace [Unreleased] with new version
    const updatedContent = content.replace(
      '## [Unreleased]',
      `## [Unreleased]

### Added
### Changed  
### Fixed
### Removed

## [${newVersion}] - ${today}`
    );
    
    writeFileSync(CHANGELOG_FILE, updatedContent);
    console.log('✓ Updated CHANGELOG.md');
  }

  /**
   * Create git tag for version
   */
  createGitTag(newVersion) {
    console.log(`Creating git tag v${newVersion}...`);
    
    try {
      execSync(`git add .`);
      execSync(`git commit -m "chore: bump version to ${newVersion}"`);
      execSync(`git tag -a v${newVersion} -m "Release v${newVersion}"`);
      console.log('✓ Created git tag');
    } catch (error) {
      console.error(`✗ Failed to create git tag: ${error.message}`);
      throw error;
    }
  }

  /**
   * Create GitHub release
   */
  async createGitHubRelease(newVersion, releaseNotes) {
    console.log(`Creating GitHub release v${newVersion}...`);
    
    try {
      const command = `gh release create v${newVersion} --title "Release v${newVersion}" --notes "${releaseNotes}"`;
      execSync(command);
      console.log('✓ Created GitHub release');
    } catch (error) {
      console.error(`✗ Failed to create GitHub release: ${error.message}`);
      throw error;
    }
  }

  /**
   * Extract release notes from changelog
   */
  extractReleaseNotes(version) {
    const content = readFileSync(CHANGELOG_FILE, 'utf8');
    const versionHeader = `## [${version}]`;
    const startIndex = content.indexOf(versionHeader);
    
    if (startIndex === -1) {
      return `Release v${version}`;
    }
    
    const nextVersionIndex = content.indexOf('\n## [', startIndex + 1);
    const endIndex = nextVersionIndex === -1 ? content.length : nextVersionIndex;
    
    return content.slice(startIndex, endIndex)
      .replace(versionHeader, '')
      .replace(/- \d{4}-\d{2}-\d{2}/, '')
      .trim();
  }

  /**
   * Main release workflow
   */
  async release(type, options = {}) {
    const { skipGit = false, skipGithub = false } = options;
    
    try {
      const newVersion = this.incrementVersion(type);
      console.log(`\nReleasing version ${newVersion} (${type} bump from ${this.currentVersion})\n`);
      
      // Update all version files
      this.updateVersionFiles(newVersion);
      
      // Update changelog
      this.updateChangelog(newVersion);
      
      // Git operations
      if (!skipGit) {
        this.createGitTag(newVersion);
      }
      
      // GitHub release
      if (!skipGithub && !skipGit) {
        const releaseNotes = this.extractReleaseNotes(newVersion);
        await this.createGitHubRelease(newVersion, releaseNotes);
      }
      
      console.log(`\n🎉 Successfully released version ${newVersion}!`);
      
      if (!skipGit) {
        console.log('\nNext steps:');
        console.log('- Push changes: git push origin main --tags');
        if (!skipGithub) {
          console.log('- GitHub release created automatically');
        }
      }
      
    } catch (error) {
      console.error(`\n❌ Release failed: ${error.message}`);
      process.exit(1);
    }
  }

  /**
   * Show current version status
   */
  status() {
    console.log(`Current version: ${this.currentVersion}\n`);
    
    console.log('Version files:');
    for (const file of VERSION_FILES) {
      try {
        let version;
        if (file.endsWith('.json')) {
          version = JSON.parse(readFileSync(file, 'utf8')).version;
        } else if (file.endsWith('.toml')) {
          const content = readFileSync(file, 'utf8');
          const match = content.match(/^version = "(.+)"$/m);
          version = match ? match[1] : 'unknown';
        }
        
        const status = version === this.currentVersion ? '✓' : '✗';
        console.log(`  ${status} ${file}: ${version}`);
      } catch (error) {
        console.log(`  ✗ ${file}: error reading file`);
      }
    }
  }
}

// CLI interface
const args = process.argv.slice(2);
const command = args[0];

const versionManager = new VersionManager();

switch (command) {
  case 'status':
    versionManager.status();
    break;
    
  case 'release':
    const type = args[1];
    if (!['major', 'minor', 'patch'].includes(type)) {
      console.error('Usage: node scripts/version-manager.mjs release <major|minor|patch>');
      process.exit(1);
    }
    
    const options = {
      skipGit: args.includes('--skip-git'),
      skipGithub: args.includes('--skip-github')
    };
    
    versionManager.release(type, options);
    break;
    
  case 'update':
    const newVersion = args[1];
    if (!newVersion) {
      console.error('Usage: node scripts/version-manager.mjs update <version>');
      process.exit(1);
    }
    versionManager.updateVersionFiles(newVersion);
    break;
    
  default:
    console.log(`
Brainless Stats Version Manager

Usage:
  node scripts/version-manager.mjs <command> [options]

Commands:
  status                    Show current version status
  release <type>           Create new release (major|minor|patch)
  update <version>         Update all files to specific version

Release Options:
  --skip-git              Skip git tag creation
  --skip-github           Skip GitHub release creation

Examples:
  node scripts/version-manager.mjs status
  node scripts/version-manager.mjs release patch
  node scripts/version-manager.mjs release minor --skip-github
  node scripts/version-manager.mjs update 1.2.3
`);
    process.exit(1);
}
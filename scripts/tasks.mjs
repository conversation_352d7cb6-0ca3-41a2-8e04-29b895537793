#!/usr/bin/env node

/**
 * Brainless Stats - Frontend Task Runner
 * 
 * This script provides frontend-specific tasks that integrate well with pnpm.
 * For backend tasks, use the Python CLI: `uv run brainless-cli`
 */

import { spawn, exec } from 'child_process';
import { promisify } from 'util';
import { existsSync, copyFileSync } from 'fs';
import { join, dirname } from 'path';
import { fileURLToPath } from 'url';

const execAsync = promisify(exec);
const __dirname = dirname(fileURLToPath(import.meta.url));
const projectRoot = join(__dirname, '..');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green');
}

function logError(message) {
  log(`❌ ${message}`, 'red');
}

function logInfo(message) {
  log(`ℹ️  ${message}`, 'blue');
}

function logWarning(message) {
  log(`⚠️  ${message}`, 'yellow');
}

async function runCommand(command, options = {}) {
  return new Promise((resolve, reject) => {
    const child = spawn(command, { 
      shell: true, 
      stdio: 'inherit',
      cwd: options.cwd || projectRoot,
      ...options 
    });
    
    child.on('close', (code) => {
      if (code === 0) {
        resolve();
      } else {
        reject(new Error(`Command failed with exit code ${code}`));
      }
    });
    
    child.on('error', reject);
  });
}

async function checkPrerequisites() {
  try {
    await execAsync('node --version');
    await execAsync('pnpm --version');
    logSuccess('Prerequisites check passed');
    return true;
  } catch (error) {
    logError('Prerequisites check failed. Please ensure Node.js and pnpm are installed.');
    return false;
  }
}

async function setupEnvironment() {
  logInfo('Setting up frontend environment...');
  
  const envFiles = [
    {
      source: join(projectRoot, '.env.example'),
      target: join(projectRoot, '.env'),
    },
    {
      source: join(projectRoot, 'frontend', '.env.example'),
      target: join(projectRoot, 'frontend', '.env'),
    },
  ];
  
  for (const { source, target } of envFiles) {
    if (!existsSync(target) && existsSync(source)) {
      copyFileSync(source, target);
      logSuccess(`Created ${target}`);
    }
  }
}

async function installDependencies() {
  logInfo('Installing frontend dependencies...');
  await runCommand('pnpm install --frozen-lockfile');
  logSuccess('Frontend dependencies installed');
}

async function generateApiSchema() {
  logInfo('Generating API schema...');
  try {
    // Check if backend is running
    await execAsync('curl -f http://localhost:8000/health', { timeout: 5000 });
    await runCommand('pnpm --filter frontend schema:generate');
    logSuccess('API schema generated');
  } catch (error) {
    logWarning('Backend not running, skipping API schema generation');
    logInfo('Start the backend first with: pnpm dev:backend');
  }
}

async function runTests(options = {}) {
  logInfo('Running frontend tests...');
  
  let command = 'pnpm --filter frontend test:run';
  
  if (options.coverage) {
    command = 'pnpm --filter frontend test:coverage';
  }
  
  if (options.ui) {
    command = 'pnpm --filter frontend test:ui';
  }
  
  if (options.e2e) {
    command = 'pnpm --filter frontend test:e2e';
  }
  
  await runCommand(command);
  logSuccess('Tests completed');
}

async function runLinting(options = {}) {
  logInfo('Running frontend linting...');
  
  if (options.fix) {
    await runCommand('pnpm --filter frontend lint --fix');
  } else {
    await runCommand('pnpm --filter frontend lint');
  }
  
  logSuccess('Linting completed');
}

async function formatCode() {
  logInfo('Formatting frontend code...');
  await runCommand('pnpm --filter frontend format');
  logSuccess('Code formatted');
}

async function buildFrontend() {
  logInfo('Building frontend...');
  await runCommand('pnpm --filter frontend build');
  logSuccess('Frontend built');
}

async function devFrontend() {
  logInfo('Starting frontend development server...');
  await runCommand('pnpm --filter frontend dev');
}

async function previewFrontend() {
  logInfo('Starting frontend preview server...');
  await runCommand('pnpm --filter frontend preview');
}

function showHelp() {
  console.log(`
${colors.bright}Brainless Stats - Frontend Task Runner${colors.reset}

${colors.cyan}Usage:${colors.reset}
  node scripts/tasks.mjs <command> [options]

${colors.cyan}Commands:${colors.reset}
  ${colors.green}setup${colors.reset}              Set up frontend environment
  ${colors.green}install${colors.reset}            Install dependencies
  ${colors.green}dev${colors.reset}                Start development server
  ${colors.green}build${colors.reset}              Build for production
  ${colors.green}preview${colors.reset}            Preview production build
  ${colors.green}test${colors.reset}               Run tests
  ${colors.green}test:ui${colors.reset}            Run tests with UI
  ${colors.green}test:e2e${colors.reset}           Run end-to-end tests
  ${colors.green}test:coverage${colors.reset}      Run tests with coverage
  ${colors.green}lint${colors.reset}               Run linting
  ${colors.green}lint:fix${colors.reset}           Run linting with auto-fix
  ${colors.green}format${colors.reset}             Format code
  ${colors.green}schema${colors.reset}             Generate API schema
  ${colors.green}help${colors.reset}               Show this help

${colors.cyan}Examples:${colors.reset}
  node scripts/tasks.mjs setup
  node scripts/tasks.mjs dev
  node scripts/tasks.mjs test:coverage
  node scripts/tasks.mjs lint:fix

${colors.yellow}Note:${colors.reset} For backend tasks, use: ${colors.cyan}cd backend && uv run brainless-cli <command>${colors.reset}
`);
}

async function main() {
  const command = process.argv[2];
  
  if (!command || command === 'help' || command === '--help' || command === '-h') {
    showHelp();
    return;
  }
  
  try {
    if (!(await checkPrerequisites())) {
      process.exit(1);
    }
    
    switch (command) {
      case 'setup':
        await setupEnvironment();
        await installDependencies();
        break;
        
      case 'install':
        await installDependencies();
        break;
        
      case 'dev':
        await devFrontend();
        break;
        
      case 'build':
        await buildFrontend();
        break;
        
      case 'preview':
        await previewFrontend();
        break;
        
      case 'test':
        await runTests();
        break;
        
      case 'test:ui':
        await runTests({ ui: true });
        break;
        
      case 'test:e2e':
        await runTests({ e2e: true });
        break;
        
      case 'test:coverage':
        await runTests({ coverage: true });
        break;
        
      case 'lint':
        await runLinting();
        break;
        
      case 'lint:fix':
        await runLinting({ fix: true });
        break;
        
      case 'format':
        await formatCode();
        break;
        
      case 'schema':
        await generateApiSchema();
        break;
        
      default:
        logError(`Unknown command: ${command}`);
        showHelp();
        process.exit(1);
    }
    
  } catch (error) {
    logError(`Task failed: ${error.message}`);
    process.exit(1);
  }
}

main();

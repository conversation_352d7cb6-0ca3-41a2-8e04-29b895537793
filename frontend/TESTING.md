# Testing Guide

This project uses a comprehensive testing strategy with <PERSON>itest for unit/integration tests and <PERSON>wright for E2E tests.

## Testing Setup

### Dependencies

- **Vitest**: Fast unit test runner with hot reload
- **@testing-library/svelte**: Component testing utilities
- **@testing-library/jest-dom**: Additional DOM matchers
- **@testing-library/user-event**: User interaction simulation
- **Playwright**: E2E testing framework
- **jsdom**: DOM environment for component tests

### Configuration Files

- `vite.config.ts`: Vitest configuration with SvelteKit integration
- `vitest-setup.js`: Global test setup and jest-dom matchers
- `playwright.config.ts`: E2E test configuration
- `tsconfig.json`: TypeScript configuration with testing types

## Test Types

### 1. Unit Tests

**Location**: `src/lib/__tests__/`
**Purpose**: Test individual functions, utilities, and modules in isolation

Example:

```typescript
// src/lib/__tests__/config.test.ts
import { describe, it, expect } from 'vitest';

describe('Configuration', () => {
	it('exports API_BASE_URL', () => {
		expect(config.API_BASE_URL).toBeDefined();
	});
});
```

### 2. Integration Tests

**Location**: `src/lib/__tests__/`
**Purpose**: Test interaction between modules, API integration, and data flow

Example:

```typescript
// src/lib/__tests__/api-integration.test.ts
import { describe, it, expect, vi } from 'vitest';

describe('API Integration', () => {
	it('handles API responses correctly', async () => {
		// Test API client behavior
	});
});
```

### 3. Component Tests (Limited Use)

**Note**: We avoid extensive component testing for shadcn-svelte UI components as they are pre-tested third-party components. Focus on testing custom application logic.

### 4. E2E Tests

**Location**: `tests/`
**Purpose**: Test complete user workflows and application behavior

Example:

```typescript
// tests/demo-upload-flow.spec.ts
import { test, expect } from '@playwright/test';

test('user can upload demo file', async ({ page }) => {
	await page.goto('/demos');
	await page.fill('[name="matchId"]', 'test-123');
	// Test complete workflow
});
```

## Running Tests

### Unit & Integration Tests

```bash
# Run all tests
npm run test

# Run tests in watch mode
npm run test:ui

# Run tests once
npm run test:run

# Run with coverage
npm run test:coverage
```

### E2E Tests

```bash
# Run E2E tests
npm run test:e2e

# Run E2E tests with UI
npm run test:e2e:ui

# Install browsers (first time)
npx playwright install
```

## Test Organization

```
frontend/
├── src/
│   ├── lib/
│   │   └── __tests__/          # Unit & integration tests
│   │       ├── config.test.ts
│   │       └── api-integration.test.ts
│   └── routes/
│       └── __tests__/          # Route-specific tests (if needed)
├── tests/                      # E2E tests
│   ├── demo-upload-flow.spec.ts
│   └── error-handling.spec.ts
├── vitest-setup.js            # Test setup
├── playwright.config.ts       # E2E config
└── vite.config.ts            # Test runner config
```

## Testing Strategies

### 1. Focus Areas

- **Business Logic**: Custom functions and data processing
- **API Integration**: Data fetching and error handling
- **User Workflows**: Complete feature flows (E2E)
- **Error Handling**: Edge cases and failure scenarios
- **Performance**: Load times and responsiveness
- **Accessibility**: Form labels, headings, keyboard navigation

### 2. What NOT to Test

- shadcn-svelte UI components (pre-tested)
- SvelteKit framework internals
- Third-party library functionality

### 3. Mock Strategy

- Mock SvelteKit runtime modules (`$app/*`)
- Mock API calls with controlled responses
- Mock environment variables for different scenarios

## Best Practices

### Unit Tests

- Test one thing at a time
- Use descriptive test names
- Mock external dependencies
- Test edge cases and error conditions

### Integration Tests

- Test data flow between modules
- Test API error handling
- Use realistic test data
- Mock external services

### E2E Tests

- Test critical user paths
- Test across different devices/viewports
- Include accessibility checks
- Test error scenarios

### General

- Keep tests fast and reliable
- Use proper test data cleanup
- Write tests that document behavior
- Maintain test readability

## Mocking SvelteKit

```typescript
// Mock navigation
vi.mock('$app/navigation', () => ({
	goto: vi.fn(),
	invalidate: vi.fn()
}));

// Mock stores
vi.mock('$app/stores', () => ({
	page: {
		subscribe: vi.fn(() => () => {})
	}
}));

// Mock environment
vi.mock('$env/dynamic/public', () => ({
	env: {
		PUBLIC_API_BASE_URL: 'http://test.example.com'
	}
}));
```

## Continuous Integration

Tests run automatically on:

- Pull requests
- Main branch pushes
- Pre-deployment

### CI Commands

```bash
# Run all tests in CI
npm run test:run
npm run test:e2e

# With coverage
npm run test:coverage
```

## Debugging Tests

### Vitest

- Use `test.only()` to focus on specific tests
- Use `console.log()` for debugging output
- Use `--ui` flag for interactive debugging

### Playwright

- Use `--debug` flag for step-by-step debugging
- Use `page.screenshot()` for visual debugging
- Use `--ui` flag for interactive test runner

## Test Coverage

Coverage reports are generated in `coverage/` directory.

Target coverage:

- **Statements**: >80%
- **Branches**: >70%
- **Functions**: >80%
- **Lines**: >80%

Focus on testing critical business logic rather than achieving 100% coverage.

## Troubleshooting

### Common Issues

1. **SvelteKit runtime errors**: Ensure proper mocking of `$app/*` modules
1. **Type errors**: Update mock types to match actual interfaces
1. **Test timeouts**: Increase timeout for slow operations
1. **Flaky E2E tests**: Add proper wait conditions

### Getting Help

- Check existing test examples
- Review SvelteKit testing documentation
- Check Playwright documentation for E2E issues
- Review vitest documentation for unit test issues

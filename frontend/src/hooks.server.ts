import { handle as jwtHandle } from '$lib/jwt.js';
import { sequence } from '@sveltejs/kit/hooks';
import { redirect, type Handle } from '@sveltejs/kit';

const authorizationHandle: Handle = async ({ event, resolve }) => {
	const routeId = event.route.id;

	if (routeId && routeId.startsWith('/(app)')) {
		const session = await event.locals.auth();
		if (!session) {
			// Redirect to the signin page
			throw redirect(303, '/login');
		}
	}

	return resolve(event);
};

export const handle: Handle = sequence(jwtHandle, authorizationHandle);

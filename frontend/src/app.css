@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
	:root {
		--background: 0 0% 100%;
		--foreground: 240 10% 3.9%;
		--muted: 240 4.8% 95.9%;
		--muted-foreground: 240 3.8% 46.1%;
		--popover: 0 0% 100%;
		--popover-foreground: 240 10% 3.9%;
		--card: 0 0% 100%;
		--card-foreground: 240 10% 3.9%;
		--border: 240 5.9% 90%;
		--input: 240 5.9% 90%;
		--primary: 240 5.9% 10%;
		--primary-foreground: 0 0% 98%;
		--secondary: 240 4.8% 95.9%;
		--secondary-foreground: 240 5.9% 10%;
		--accent: 240 4.8% 95.9%;
		--accent-foreground: 240 5.9% 10%;
		--destructive: 0 72.2% 50.6%;
		--destructive-foreground: 0 0% 98%;
		--ring: 240 10% 3.9%;
		--radius: 0.5rem;
		--sidebar-background: 0 0% 98%;
		--sidebar-foreground: 240 5.3% 26.1%;
		--sidebar-primary: 240 5.9% 10%;
		--sidebar-primary-foreground: 0 0% 98%;
		--sidebar-accent: 240 4.8% 95.9%;
		--sidebar-accent-foreground: 240 5.9% 10%;
		--sidebar-border: 220 13% 91%;
		--sidebar-ring: 217.2 91.2% 59.8%;
	}

	.dark {
		--background: 240 10% 3.9%;
		--foreground: 0 0% 98%;
		--muted: 240 3.7% 15.9%;
		--muted-foreground: 240 5% 64.9%;
		--popover: 240 10% 3.9%;
		--popover-foreground: 0 0% 98%;
		--card: 240 10% 3.9%;
		--card-foreground: 0 0% 98%;
		--border: 240 3.7% 15.9%;
		--input: 240 3.7% 15.9%;
		--primary: 0 0% 98%;
		--primary-foreground: 240 5.9% 10%;
		--secondary: 240 3.7% 15.9%;
		--secondary-foreground: 0 0% 98%;
		--accent: 240 3.7% 15.9%;
		--accent-foreground: 0 0% 98%;
		--destructive: 0 62.8% 30.6%;
		--destructive-foreground: 0 0% 98%;
		--ring: 240 4.9% 83.9%;
		--sidebar-background: 240 5.9% 10%;
		--sidebar-foreground: 240 4.8% 95.9%;
		--sidebar-primary: 224.3 76.3% 48%;
		--sidebar-primary-foreground: 0 0% 100%;
		--sidebar-accent: 240 3.7% 15.9%;
		--sidebar-accent-foreground: 240 4.8% 95.9%;
		--sidebar-border: 240 3.7% 15.9%;
		--sidebar-ring: 217.2 91.2% 59.8%;
	}
}

@layer base {
	:root {
		--sidebar-background: 0 0% 98%;
		--sidebar-foreground: 240 5.3% 26.1%;
		--sidebar-primary: 240 5.9% 10%;
		--sidebar-primary-foreground: 0 0% 98%;
		--sidebar-accent: 240 4.8% 95.9%;
		--sidebar-accent-foreground: 240 5.9% 10%;
		--sidebar-border: 220 13% 91%;
		--sidebar-ring: 217.2 91.2% 59.8%;
	}

	.dark {
		--sidebar-background: 240 5.9% 10%;
		--sidebar-foreground: 240 4.8% 95.9%;
		--sidebar-primary: 224.3 76.3% 48%;
		--sidebar-primary-foreground: 0 0% 100%;
		--sidebar-accent: 240 3.7% 15.9%;
		--sidebar-accent-foreground: 240 4.8% 95.9%;
		--sidebar-border: 240 3.7% 15.9%;
		--sidebar-ring: 217.2 91.2% 59.8%;
	}
}

@layer base {
	* {
		@apply border-border;
	}

	html {
		@apply antialiased;
	}

	body {
		@apply bg-background text-foreground;
		font-feature-settings:
			'rlig' 1,
			'calt' 1;
	}
}

@layer base {
	@font-face {
		font-family: 'Baunk';
		src: local('Baunk'), local('baunk'), url('/fonts/baunk/Baunk.ttf');
		font-weight: normal;
		font-display: swap;
	}

	@font-face {
		font-family: 'Alterra Regular';
		src:
			local('Alterra'),
			local('LD Alterra'),
			local('LD_Alterra'),
			url('/fonts/alterra/LD_Alterra.ttf') format('truetype');
		font-weight: 400;
		font-style: normal;
		font-display: swap;
	}

	@font-face {
		font-family: 'Alterra Cut';
		src:
			local('Alterra Cut'),
			local('LD Alterra Cut'),
			local('LD_Alterra_Cut'),
			url('/fonts/alterra/LD_Alterra_Cut.ttf') format('truetype');
		font-weight: normal;
		font-display: swap;
	}

	@font-face {
		font-family: 'Centrion';
		src: url('/fonts/centrion/Centrion Variable.ttf') format('truetype-variations');
		font-weight: 100 900;
		font-stretch: 75% 125%;
		font-style: normal;
		font-display: swap;
	}

	@font-face {
		font-family: 'Miratrix';
		src: url('/fonts/miratrix/Miratrix-Normal.otf');
		font-weight: normal;
		font-display: swap;
	}
}

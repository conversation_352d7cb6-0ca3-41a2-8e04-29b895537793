import { redirect } from '@sveltejs/kit';
import {
	verifySteamResponse,
	extractSteamId,
	validateOpenIDResponse,
	parseSteamOpenIDParams
} from '$lib/steam_auth';
import { setAuthCookies } from '$lib/jwt';
import { registerUser, isUserRegistered, getUserBySteamId } from '$lib/users';
import client from '$lib/api';
import type { PageServerLoad } from './$types';

export const load: PageServerLoad = async ({ url, fetch, cookies }) => {
	const params = parseSteamOpenIDParams(url.searchParams);

	if (!params) {
		throw redirect(302, '/login?error=invalid_steam_response');
	}

	// Validate OpenID response
	if (!validateOpenIDResponse(params)) {
		throw redirect(302, '/login?error=invalid_steam_response');
	}

	const isValid = await verifySteamResponse(params, fetch);

	if (isValid) {
		const steamId = extractSteamId(params['openid.claimed_id']);
		console.log(steamId);
		if (await isUserRegistered(steamId)) {
			const user = await getUserBySteamId(steamId);
			if (user) {
				await setAuthCookies(cookies, String(user.id));
				// Check if user has tracking configured, if not redirect to settings
				const { data: userData } = await client.GET('/api/users/{user_id}', {
					params: { path: { user_id: user.id! } }
				});
				if (userData && !userData.has_tracking) {
					throw redirect(302, '/settings?setup=tracking');
				}
				throw redirect(302, '/');
			}
		}

		await registerUser(steamId);
		const user = await getUserBySteamId(steamId);
		if (user) {
			await setAuthCookies(cookies, String(user.id));
			// New users should always be redirected to settings to configure tracking
			throw redirect(302, '/settings?setup=tracking');
		}

		throw redirect(302, `/`);
	} else {
		throw redirect(302, '/login?error=invalid_steam_login');
	}
};

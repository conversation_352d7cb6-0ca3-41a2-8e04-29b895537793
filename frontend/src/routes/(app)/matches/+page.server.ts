import type { PageServerLoad } from './$types';
import client from '$lib/api';

export const load: PageServerLoad = async () => {
	try {
		const { data: matches, error } = await client.GET('/api/matches', {});

		if (error) {
			console.error('Error fetching matches:', error);
			return { matches: [] };
		}

		return { matches: matches || [] };
	} catch (error) {
		console.error('Error in matches page load:', error);
		return { matches: [] };
	}
};

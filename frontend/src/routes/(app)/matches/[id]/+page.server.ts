import type { PageServerLoad } from './$types';
import client from '$lib/api';
import { error } from '@sveltejs/kit';

export const load: PageServerLoad = async ({ params }) => {
	try {
		const matchId = parseInt(params.id);
		if (isNaN(matchId)) {
			throw error(400, 'Invalid match ID');
		}

		const { data: match, error: apiError } = await client.GET('/api/matches/{match_id}', {
			params: {
				path: { match_id: matchId }
			}
		});

		if (apiError) {
			console.error('Error fetching match:', apiError);
			throw error(404, 'Match not found');
		}

		return { match };
	} catch (err) {
		console.error('Error in match details page load:', err);
		if (err instanceof Response) {
			throw err;
		}
		throw error(500, 'Internal server error');
	}
};

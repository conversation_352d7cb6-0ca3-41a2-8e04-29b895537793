<script lang="ts">
	import * as Card from '$lib/components/ui/card';
	import * as Table from '$lib/components/ui/table';
	import { Button } from '$lib/components/ui/button';
	import { Badge } from '$lib/components/ui/badge';
	import { Progress } from '$lib/components/ui/progress';
	import { Separator } from '$lib/components/ui/separator';
	import { ArrowLeft, Trophy, Timer, BarChart3, Activity, Map } from 'lucide-svelte';
	import type { PageData } from './$types';

	interface Props {
		data: PageData;
	}

	let { data }: Props = $props();

	// Use real match data from the server
	let match = data.match;

	let finalScore = $derived(() => {
		if (match.rounds.length === 0) return 'N/A';
		const lastRound = match.rounds[match.rounds.length - 1];
		if (!lastRound) return 'N/A';
		return `${lastRound.ct_score} - ${lastRound.t_score}`;
	});

	let matchWinner = $derived(() => {
		if (match.rounds.length === 0) return 'N/A';
		const lastRound = match.rounds[match.rounds.length - 1];
		if (!lastRound) return 'N/A';
		return lastRound.ct_score > lastRound.t_score ? 'CT' : 'T';
	});

	let ctPlayers = $derived(match.players.filter((p) => p.team === 'CT'));
	let tPlayers = $derived(match.players.filter((p) => p.team === 'T'));

	function formatDate(dateString: string) {
		return new Date(dateString).toLocaleDateString('en-US', {
			weekday: 'long',
			year: 'numeric',
			month: 'long',
			day: 'numeric',
			hour: '2-digit',
			minute: '2-digit'
		});
	}

	function getTeamPlayerCount(team: string) {
		return match.players.filter((p) => p.team === team).length;
	}
</script>

<svelte:head>
	<title>Match {match.id} - {match.map_name} - Brainless Stats</title>
</svelte:head>

<div class="container mx-auto space-y-6 p-6">
	<!-- Header -->
	<div class="flex items-center gap-4">
		<Button href="/matches" variant="outline" size="sm" class="gap-2">
			<ArrowLeft class="h-4 w-4" />
			Back to Matches
		</Button>
		<div class="flex-1">
			<h1 class="text-3xl font-bold tracking-tight">Match Analysis</h1>
			<p class="text-muted-foreground">
				{match.map_name} • {match.created_at ? formatDate(match.created_at) : 'Unknown date'}
			</p>
		</div>
	</div>

	<!-- Match Overview -->
	<div class="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
		<Card.Root>
			<Card.Header class="flex flex-row items-center justify-between space-y-0 pb-2">
				<Card.Title class="text-sm font-medium">Final Score</Card.Title>
				<Trophy class="h-4 w-4 text-muted-foreground" />
			</Card.Header>
			<Card.Content>
				<div class="text-2xl font-bold">{finalScore}</div>
				<p class="text-xs text-muted-foreground">
					Winner: <span class="font-medium">{matchWinner}</span>
				</p>
			</Card.Content>
		</Card.Root>

		<Card.Root>
			<Card.Header class="flex flex-row items-center justify-between space-y-0 pb-2">
				<Card.Title class="text-sm font-medium">Map</Card.Title>
				<Map class="h-4 w-4 text-muted-foreground" />
			</Card.Header>
			<Card.Content>
				<div class="text-2xl font-bold">{match.map_name}</div>
				<p class="text-xs text-muted-foreground">Active Duty</p>
			</Card.Content>
		</Card.Root>

		<Card.Root>
			<Card.Header class="flex flex-row items-center justify-between space-y-0 pb-2">
				<Card.Title class="text-sm font-medium">Rounds</Card.Title>
				<Activity class="h-4 w-4 text-muted-foreground" />
			</Card.Header>
			<Card.Content>
				<div class="text-2xl font-bold">{match.rounds.length}</div>
				<p class="text-xs text-muted-foreground">Total rounds played</p>
			</Card.Content>
		</Card.Root>

		<Card.Root>
			<Card.Header class="flex flex-row items-center justify-between space-y-0 pb-2">
				<Card.Title class="text-sm font-medium">Duration</Card.Title>
				<Timer class="h-4 w-4 text-muted-foreground" />
			</Card.Header>
			<Card.Content>
				<div class="text-2xl font-bold">
					{match.rounds.length > 0 ? `${Math.round(match.rounds.length * 1.5)}m` : 'N/A'}
				</div>
				<p class="text-xs text-muted-foreground">Match duration</p>
			</Card.Content>
		</Card.Root>
	</div>

	<!-- Team Performance -->
	<div class="grid gap-6 lg:grid-cols-2">
		<!-- Counter-Terrorists -->
		<Card.Root>
			<Card.Header>
				<Card.Title class="flex items-center gap-2">
					<div class="h-4 w-4 rounded bg-blue-500"></div>
					Counter-Terrorists
				</Card.Title>
				<div class="flex items-center gap-4 text-sm text-muted-foreground">
					<span>Players: {getTeamPlayerCount('CT')}</span>
				</div>
			</Card.Header>
			<Card.Content>
				<div class="space-y-2">
					{#each ctPlayers as player}
						<div class="flex items-center justify-between rounded-lg bg-muted/50 p-3">
							<div class="font-medium">{player.name}</div>
							<div class="text-sm text-muted-foreground">{player.steam_id}</div>
						</div>
					{/each}
				</div>
			</Card.Content>
		</Card.Root>

		<!-- Terrorists -->
		<Card.Root>
			<Card.Header>
				<Card.Title class="flex items-center gap-2">
					<div class="h-4 w-4 rounded bg-orange-500"></div>
					Terrorists
				</Card.Title>
				<div class="flex items-center gap-4 text-sm text-muted-foreground">
					<span>Players: {getTeamPlayerCount('T')}</span>
				</div>
			</Card.Header>
			<Card.Content>
				<div class="space-y-2">
					{#each tPlayers as player}
						<div class="flex items-center justify-between rounded-lg bg-muted/50 p-3">
							<div class="font-medium">{player.name}</div>
							<div class="text-sm text-muted-foreground">{player.steam_id}</div>
						</div>
					{/each}
				</div>
			</Card.Content>
		</Card.Root>
	</div>

	<!-- Round History -->
	<Card.Root>
		<Card.Header>
			<Card.Title class="flex items-center gap-2">
				<BarChart3 class="h-5 w-5" />
				Round History
			</Card.Title>
			<Card.Description>Round-by-round progression and outcomes</Card.Description>
		</Card.Header>
		<Card.Content>
			<div class="space-y-4">
				<!-- Score Progress -->
				<div class="grid grid-cols-2 gap-4">
					<div>
						<div class="mb-2 flex items-center justify-between">
							<span class="flex items-center gap-2 text-sm font-medium">
								<div class="h-3 w-3 rounded bg-blue-500"></div>
								Counter-Terrorists
							</span>
							<span class="text-sm font-bold"
								>{match.rounds[match.rounds.length - 1]?.ct_score ?? 0}</span
							>
						</div>
						<Progress
							value={((match.rounds[match.rounds.length - 1]?.ct_score ?? 0) / 16) * 100}
							class="h-2"
						/>
					</div>
					<div>
						<div class="mb-2 flex items-center justify-between">
							<span class="flex items-center gap-2 text-sm font-medium">
								<div class="h-3 w-3 rounded bg-orange-500"></div>
								Terrorists
							</span>
							<span class="text-sm font-bold"
								>{match.rounds[match.rounds.length - 1]?.t_score ?? 0}</span
							>
						</div>
						<Progress
							value={((match.rounds[match.rounds.length - 1]?.t_score ?? 0) / 16) * 100}
							class="h-2"
						/>
					</div>
				</div>

				<!-- Round Visualization -->
				<div class="grid-cols-15 grid gap-1">
					{#each match.rounds.slice(0, 30) as round, i}
						<div
							class="flex aspect-square items-center justify-center rounded text-xs font-bold text-white {round.winner ===
							'CT'
								? 'bg-blue-500'
								: 'bg-orange-500'}"
							title="Round {round.round_number}: {round.winner} won by {round.win_reason}"
						>
							{round.round_number}
						</div>
					{/each}
				</div>

				<div class="flex items-center gap-4 text-xs text-muted-foreground">
					<div class="flex items-center gap-2">
						<div class="h-3 w-3 rounded bg-blue-500"></div>
						<span>CT Win</span>
					</div>
					<div class="flex items-center gap-2">
						<div class="h-3 w-3 rounded bg-orange-500"></div>
						<span>T Win</span>
					</div>
				</div>
			</div>
		</Card.Content>
	</Card.Root>
</div>

<style>
	.grid-cols-15 {
		grid-template-columns: repeat(15, minmax(0, 1fr));
	}
</style>

<script lang="ts">
	import * as Sidebar from '$lib/components/ui/sidebar/index.js';
	import AppSidebar from '$lib/components/app-sidebar.svelte';
	import type { LayoutProps } from './$types';

	let { children, data }: LayoutProps = $props();

	const user = data.user
		? {
				username: data.user.username ?? 'Unknown User',
				profile_picture_url: data.user.profile_picture_url ?? '/default-avatar.png'
			}
		: {
				username: 'Guest',
				profile_picture_url: '/default-avatar.png'
			};
</script>

<Sidebar.Provider>
	<AppSidebar {user} />
	<main class="ml-2">
		{#if data.user}
			<Sidebar.Trigger />
			{@render children?.()}
		{/if}
	</main>
</Sidebar.Provider>

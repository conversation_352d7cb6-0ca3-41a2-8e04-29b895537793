import type { PageServerLoad, Actions } from './$types';
import client from '$lib/api';
import { fail } from '@sveltejs/kit';
import { superValidate } from 'sveltekit-superforms';
import { zod } from 'sveltekit-superforms/adapters';
import { z } from 'zod';
import { API_BASE_URL } from '$lib/config';
// Import client schema - Define inline to avoid Svelte import issues
const clientSchema = z.object({
	matchId: z.string().min(1, { message: 'Match ID is required.' }),
	demoFile: z.any().optional() // Use z.any() to avoid FileList reference in SSR
});

// Server-side schema for action validation (expects File object)
const serverActionSchema = z.object({
	matchId: z.string().min(1, { message: 'Match ID is required.' }),
	demoFile: z
		.instanceof(File, { message: 'A .dem file is required.' })
		.refine((file) => file.size > 0, { message: 'Demo file cannot be empty.' })
		.refine((file) => file.name.endsWith('.dem'), {
			message: 'File must be a .dem file.'
		})
});

export const load: PageServerLoad = async () => {
	// Removed unused 'locals'
	try {
		const form = await superValidate(zod(clientSchema)); // Use client schema for form init
		const { data: demos, error } = await client.GET('/api/demos', {}); // Added empty object as second argument

		if (error) {
			console.error('Error fetching demos:', error);
			return { form, demos: [] };
		}

		return { form, demos };
	} catch (error) {
		console.error('Error in demos page load:', error);
		const form = await superValidate(zod(clientSchema)); // Ensure form is always returned
		return { form, demos: [] };
	}
};

export const actions: Actions = {
	upload: async ({ request }) => {
		const form = await superValidate(request, zod(serverActionSchema));

		if (!form.valid) {
			return fail(400, { form, success: false, message: 'Invalid form submission.' });
		}

		const { matchId, demoFile } = form.data;

		try {
			const apiFormData = new FormData();
			apiFormData.append('demo_file', demoFile); // demoFile is now a File object

			// Make the API request
			// Ensure your API endpoint for upload is correct and handles match_id as query param or in FormData
			const response = await fetch(
				`${API_BASE_URL}/api/demos/upload?match_id=${encodeURIComponent(matchId)}`,
				{
					method: 'POST',
					body: apiFormData
				}
			);

			if (!response.ok) {
				const errorData = await response
					.json()
					.catch(() => ({ detail: 'Failed to upload demo file and parse error response' }));
				return fail(response.status, {
					form,
					success: false,
					message: errorData.detail || 'Failed to upload demo file'
				});
			}

			// Assuming the API returns JSON on success as well, or adjust as needed
			// const successData = await response.json();

			return {
				form,
				success: true,
				message: 'Demo uploaded and processing started!',
				redirect: '/matches'
			};
		} catch (error) {
			console.error('Error uploading demo file:', error);
			return fail(500, {
				form,
				success: false,
				message: 'An unexpected error occurred during upload'
			});
		}
	}
};

<script lang="ts">
	import * as Card from '$lib/components/ui/card';
	import * as Form from '$lib/components/ui/form';
	import { Input } from '$lib/components/ui/input';
	import { Button } from '$lib/components/ui/button';
	import { Separator } from '$lib/components/ui/separator';
	import { Switch } from '$lib/components/ui/switch';
	import { Palette, Monitor, Moon, Sun, Gamepad2 } from 'lucide-svelte';
	import { toggleMode } from 'mode-watcher';
	import { superForm } from 'sveltekit-superforms';
	import { zodClient } from 'sveltekit-superforms/adapters';
	import { addTrackingSchema } from './index.js';
	import type { PageData } from './$types.js';

	const { data }: { data: PageData } = $props();

	const form = superForm(data.form, {
		validators: zodClient(addTrackingSchema)
	});

	const { form: formData, enhance, errors, message } = form;

	// Check if user is being prompted to set up tracking
	let isTrackingSetup = $state(false);
	$effect(() => {
		if (typeof window !== 'undefined') {
			const urlParams = new URLSearchParams(window.location.search);
			isTrackingSetup = urlParams.get('setup') === 'tracking';
		}
	});

	// Settings state for appearance
	let preferences = $state({
		compactView: false
	});
</script>

<svelte:head>
	<title>Settings - Brainless Stats</title>
</svelte:head>

<div class="container mx-auto space-y-6 p-6">
	<!-- Header -->
	<div class="flex flex-col gap-2">
		<h1 class="text-3xl font-bold tracking-tight">Settings</h1>
		<p class="text-muted-foreground">Manage your account settings and preferences</p>
		{#if isTrackingSetup}
			<div class="rounded-lg border-l-4 border-l-blue-500 bg-blue-50 p-4">
				<div class="flex">
					<div class="ml-3">
						<p class="text-sm font-medium text-blue-800">
							Welcome! Let's set up your CS2 tracking configuration
						</p>
						<p class="mt-1 text-sm text-blue-700">
							Configure your authentication code and match token below to start tracking your CS2
							matches automatically.
						</p>
					</div>
				</div>
			</div>
		{/if}
	</div>

	<div class="space-y-6">
		<!-- CS2 Tracking Configuration -->
		<Card.Root>
			<Card.Header>
				<Card.Title class="flex items-center gap-2">
					<Gamepad2 class="h-5 w-5" />
					CS2 Match Tracking
				</Card.Title>
				<Card.Description>
					Configure your CS2 authentication code and match token for automatic match tracking
				</Card.Description>
			</Card.Header>
			<Card.Content>
				<form method="POST" use:enhance class="space-y-4">
					<Form.Field {form} name="authenticationCode">
						<Form.Control>
							{#snippet children(attrs)}
								<Form.Label>Authentication Code</Form.Label>
								<Input
									{...attrs}
									name="authenticationCode"
									bind:value={$formData.authenticationCode}
									placeholder="XXXX-XXXXX-XXXX"
									class="font-mono"
								/>
							{/snippet}
						</Form.Control>
						<Form.Description>
							Your CS2 authentication code from the game console. Get this by typing
							<code class="rounded bg-muted px-1 text-sm">status</code> in the CS2 console.
							<br />
							<a
								href="https://help.steampowered.com/en/wizard/HelpWithGameIssue/?appid=730&issueid=128"
								target="_blank"
								rel="noopener noreferrer"
								class="text-blue-600 underline hover:text-blue-800"
							>
								Need help finding this? Click here for Steam's guide →
							</a>
						</Form.Description>
						<Form.FieldErrors />
					</Form.Field>

					<Form.Field {form} name="initialMatchToken">
						<Form.Control>
							{#snippet children(attrs)}
								<Form.Label>Initial Match Token</Form.Label>
								<Input
									{...attrs}
									name="initialMatchToken"
									bind:value={$formData.initialMatchToken}
									placeholder="CSGO-XXXXX-XXXXX-XXXXX-XXXXX-XXXXX"
									class="font-mono"
								/>
							{/snippet}
						</Form.Control>
						<Form.Description>
							A recent match share code from CS2. Get this by going to Watch > Your Matches and
							copying a share code.
						</Form.Description>
						<Form.FieldErrors />
					</Form.Field>

					{#if $message}
						<div class="rounded-md bg-green-50 p-3 text-sm text-green-700">
							{$message}
						</div>
					{/if}

					{#if $errors._errors}
						<div class="rounded-md bg-red-50 p-3 text-sm text-red-700">
							{$errors._errors.join(', ')}
						</div>
					{/if}

					<Form.Button class="w-full">Save Tracking Configuration</Form.Button>
				</form>
			</Card.Content>
		</Card.Root>

		<!-- Appearance Settings -->
		<Card.Root>
			<Card.Header>
				<Card.Title class="flex items-center gap-2">
					<Palette class="h-5 w-5" />
					Appearance
				</Card.Title>
				<Card.Description>Customize the look and feel of the application</Card.Description>
			</Card.Header>
			<Card.Content class="space-y-4">
				<div class="space-y-3">
					<label class="text-sm font-medium">Theme</label>
					<div class="grid grid-cols-3 gap-3">
						<Button variant="outline" class="h-auto gap-2 py-3" onclick={toggleMode}>
							<Sun class="h-4 w-4" />
							<div class="text-center">
								<div class="font-medium">Light</div>
								<div class="text-xs text-muted-foreground">Bright theme</div>
							</div>
						</Button>
						<Button variant="outline" class="h-auto gap-2 py-3" onclick={toggleMode}>
							<Moon class="h-4 w-4" />
							<div class="text-center">
								<div class="font-medium">Dark</div>
								<div class="text-xs text-muted-foreground">Dark theme</div>
							</div>
						</Button>
						<Button variant="outline" class="h-auto gap-2 py-3" onclick={toggleMode}>
							<Monitor class="h-4 w-4" />
							<div class="text-center">
								<div class="font-medium">System</div>
								<div class="text-xs text-muted-foreground">Follow system</div>
							</div>
						</Button>
					</div>
				</div>
				<Separator />
				<div class="flex items-center justify-between">
					<div class="space-y-1">
						<label class="text-sm font-medium">Compact View</label>
						<p class="text-sm text-muted-foreground">
							Use a more compact layout for tables and lists
						</p>
					</div>
					<Switch bind:checked={preferences.compactView} />
				</div>
			</Card.Content>
		</Card.Root>
	</div>
</div>

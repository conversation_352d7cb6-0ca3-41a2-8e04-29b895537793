import type { PageServerLoad, Actions } from './$types.js';
// import type { UserResponse, TrackingDetailsResponse } from '$lib/types/api';
import { fail } from '@sveltejs/kit';
import { superValidate } from 'sveltekit-superforms';
import { addTrackingSchema } from './index';
import client from '$lib/api';
import { zod } from 'sveltekit-superforms/adapters';

export const load: PageServerLoad = async ({ locals }) => {
	const userId = await locals.auth();
	if (!userId) {
		return {
			form: await superValidate(zod(addTrackingSchema))
		};
	}

	try {
		const { data: userData, error: userError } = await client.GET('/api/users/{user_id}', {
			params: { path: { user_id: Number(userId) } }
		});

		// If user has no tracking, return empty form
		if (!userData?.has_tracking) {
			return {
				form: await superValidate(zod(addTrackingSchema))
			};
		}

		// Fetch tracking details
		const { data: trackingData } = await client.GET(`/api/users/{user_id}/tracking`, {
			params: { path: { user_id: Number(userId) } }
		});

		const data =
			trackingData && !userError
				? {
						authenticationCode: trackingData.authentication_code,
						initialMatchToken: trackingData.initial_match_token
					}
				: {};

		return {
			form: await superValidate(data, zod(addTrackingSchema))
		};
	} catch {
		return { form: await superValidate(zod(addTrackingSchema)) };
	}
};

export const actions: Actions = {
	default: async ({ request, locals }: { request: Request; locals: App.Locals }) => {
		const form = await superValidate(request, zod(addTrackingSchema));
		if (!form.valid) {
			return fail(400, { form });
		}
		const userId = await locals.auth();
		if (!userId) {
			return fail(401, {
				form,
				error: 'You must be logged in to update tracking settings'
			});
		}

		const { response, error } = await client.POST('/api/users/{user_id}/tracking', {
			params: {
				path: {
					user_id: Number(userId)
				}
			},
			body: {
				authentication_code: form.data.authenticationCode,
				initial_match_token: form.data.initialMatchToken,
				user_id: Number(userId)
			}
		});

		if (error) {
			if (response.status === 422) {
				return fail(422, { form, error: 'Invalid input data' });
			}
			return fail(response.status, {
				form,
				error: response.status === 409 ? 'Tracking already exists' : 'Failed to create tracking'
			});
		}

		return { form };
	}
};

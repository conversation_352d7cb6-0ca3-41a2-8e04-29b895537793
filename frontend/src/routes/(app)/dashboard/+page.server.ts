import { redirect } from '@sveltejs/kit';
import type { PageServerLoad } from './$types';
import client from '$lib/api';

export const load: PageServerLoad = async ({ locals }) => {
	try {
		// Fetch dashboard overview data in parallel
		const [{ data: matches, error: matchesError }, { data: demos, error: demosError }] =
			await Promise.all([client.GET('/api/matches', {}), client.GET('/api/demos', {})]);

		if (matchesError) {
			console.error('Error fetching matches:', matchesError);
		}
		if (demosError) {
			console.error('Error fetching demos:', demosError);
		}

		// Calculate dashboard statistics
		const totalMatches = matches?.length || 0;
		const totalDemos = demos?.length || 0;
		const processedDemos = demos?.filter((demo) => demo.status === 'PROCESSED').length || 0;
		const processingDemos = demos?.filter((demo) => demo.status === 'PROCESSING').length || 0;
		const failedDemos = demos?.filter((demo) => demo.status === 'FAILED').length || 0;

		// Get recent matches (last 5)
		const recentMatches = matches?.slice(0, 5) || [];

		// Get recent demos (last 5)
		const recentDemos = demos?.slice(0, 5) || [];

		// Calculate processing success rate
		const successRate = totalDemos > 0 ? Math.round((processedDemos / totalDemos) * 100) : 0;

		return {
			stats: {
				totalMatches,
				totalDemos,
				processedDemos,
				processingDemos,
				failedDemos,
				successRate
			},
			recentMatches,
			recentDemos
		};
	} catch (error) {
		console.error('Error in dashboard page load:', error);
		return {
			stats: {
				totalMatches: 0,
				totalDemos: 0,
				processedDemos: 0,
				processingDemos: 0,
				failedDemos: 0,
				successRate: 0
			},
			recentMatches: [],
			recentDemos: []
		};
	}
};

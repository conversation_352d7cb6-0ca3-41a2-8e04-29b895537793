<script lang="ts">
	import * as Card from '$lib/components/ui/card';
	import * as Table from '$lib/components/ui/table';
	import { Button } from '$lib/components/ui/button';
	import { Badge } from '$lib/components/ui/badge';
	import { Progress } from '$lib/components/ui/progress';
	import {
		Upload,
		FileText,
		TrendingUp,
		Clock,
		CheckCircle,
		XCircle,
		Play,
		Eye,
		Activity,
		BarChart3
	} from 'lucide-svelte';

	interface DashboardStats {
		totalMatches: number;
		totalDemos: number;
		processedDemos: number;
		processingDemos: number;
		failedDemos: number;
		successRate: number;
	}

	interface Match {
		id: string;
		map_name: string;
		created_at: string;
		rounds: Array<{
			ct_score: number;
			t_score: number;
		}>;
	}

	interface Demo {
		match_id: string;
		status: string;
		created_at: string;
	}

	interface DashboardPageData {
		stats: DashboardStats;
		recentMatches: Match[];
		recentDemos: Demo[];
		user?: {
			steam_id: string;
			username?: string | null;
			profile_picture_url?: string | null;
			created_at?: string;
			id: number;
			has_tracking: boolean;
		};
	}

	interface Props {
		data: DashboardPageData;
	}

	let { data }: Props = $props();

	function getStatusColor(status: string) {
		switch (status) {
			case 'PROCESSED':
				return 'bg-green-500';
			case 'PROCESSING':
				return 'bg-yellow-500';
			case 'FAILED':
				return 'bg-red-500';
			default:
				return 'bg-gray-500';
		}
	}

	function getStatusVariant(status: string): 'default' | 'secondary' | 'destructive' | 'outline' {
		switch (status) {
			case 'PROCESSED':
				return 'default';
			case 'PROCESSING':
				return 'secondary';
			case 'FAILED':
				return 'destructive';
			default:
				return 'outline';
		}
	}

	function formatDate(dateString: string) {
		return new Date(dateString).toLocaleDateString('en-US', {
			month: 'short',
			day: 'numeric',
			hour: '2-digit',
			minute: '2-digit'
		});
	}

	function getMatchScore(match: any) {
		if (!match.rounds || match.rounds.length === 0) return 'N/A';
		const lastRound = match.rounds[match.rounds.length - 1];
		return `${lastRound.ct_score} - ${lastRound.t_score}`;
	}
</script>

<svelte:head>
	<title>Dashboard - Brainless Stats</title>
</svelte:head>

<div class="container mx-auto space-y-6 p-6">
	<!-- Header -->
	<div class="flex flex-col gap-2">
		<h1 class="text-3xl font-bold tracking-tight">Dashboard</h1>
		<p class="text-muted-foreground">Welcome back! Here's your CS2 analytics overview.</p>
	</div>

	<!-- Quick Actions -->
	<div class="flex gap-3">
		<Button href="/demos" class="gap-2">
			<Upload class="h-4 w-4" />
			Upload Demo
		</Button>
		<Button href="/matches" variant="outline" class="gap-2">
			<Eye class="h-4 w-4" />
			View Matches
		</Button>
	</div>

	<!-- Statistics Cards -->
	<div class="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
		<!-- Total Matches -->
		<Card.Root>
			<Card.Header class="flex flex-row items-center justify-between space-y-0 pb-2">
				<Card.Title class="text-sm font-medium">Total Matches</Card.Title>
				<BarChart3 class="h-4 w-4 text-muted-foreground" />
			</Card.Header>
			<Card.Content>
				<div class="text-2xl font-bold">{data.stats.totalMatches}</div>
				<p class="text-xs text-muted-foreground">Analyzed matches</p>
			</Card.Content>
		</Card.Root>

		<!-- Demo Files -->
		<Card.Root>
			<Card.Header class="flex flex-row items-center justify-between space-y-0 pb-2">
				<Card.Title class="text-sm font-medium">Demo Files</Card.Title>
				<FileText class="h-4 w-4 text-muted-foreground" />
			</Card.Header>
			<Card.Content>
				<div class="text-2xl font-bold">{data.stats.totalDemos}</div>
				<p class="text-xs text-muted-foreground">Uploaded demos</p>
			</Card.Content>
		</Card.Root>

		<!-- Processing Success Rate -->
		<Card.Root>
			<Card.Header class="flex flex-row items-center justify-between space-y-0 pb-2">
				<Card.Title class="text-sm font-medium">Success Rate</Card.Title>
				<TrendingUp class="h-4 w-4 text-muted-foreground" />
			</Card.Header>
			<Card.Content>
				<div class="text-2xl font-bold">{data.stats.successRate}%</div>
				<p class="text-xs text-muted-foreground">Processing success</p>
			</Card.Content>
		</Card.Root>

		<!-- Processing Queue -->
		<Card.Root>
			<Card.Header class="flex flex-row items-center justify-between space-y-0 pb-2">
				<Card.Title class="text-sm font-medium">In Queue</Card.Title>
				<Clock class="h-4 w-4 text-muted-foreground" />
			</Card.Header>
			<Card.Content>
				<div class="text-2xl font-bold">{data.stats.processingDemos}</div>
				<p class="text-xs text-muted-foreground">Being processed</p>
			</Card.Content>
		</Card.Root>
	</div>

	<!-- Processing Overview -->
	{#if data.stats.totalDemos > 0}
		<Card.Root>
			<Card.Header>
				<Card.Title class="flex items-center gap-2">
					<Activity class="h-5 w-5" />
					Processing Overview
				</Card.Title>
			</Card.Header>
			<Card.Content class="space-y-4">
				<div class="grid gap-4 md:grid-cols-3">
					<div class="flex items-center gap-2">
						<CheckCircle class="h-4 w-4 text-green-500" />
						<span class="text-sm">Processed: {data.stats.processedDemos}</span>
					</div>
					<div class="flex items-center gap-2">
						<Clock class="h-4 w-4 text-yellow-500" />
						<span class="text-sm">Processing: {data.stats.processingDemos}</span>
					</div>
					<div class="flex items-center gap-2">
						<XCircle class="h-4 w-4 text-red-500" />
						<span class="text-sm">Failed: {data.stats.failedDemos}</span>
					</div>
				</div>
				<div class="space-y-2">
					<div class="flex justify-between text-sm">
						<span>Processing Success Rate</span>
						<span>{data.stats.successRate}%</span>
					</div>
					<Progress value={data.stats.successRate} class="h-2" />
				</div>
			</Card.Content>
		</Card.Root>
	{/if}

	<div class="grid gap-6 lg:grid-cols-2">
		<!-- Recent Matches -->
		<Card.Root>
			<Card.Header class="flex flex-row items-center justify-between">
				<div>
					<Card.Title>Recent Matches</Card.Title>
					<Card.Description>Your latest analyzed matches</Card.Description>
				</div>
				<Button href="/matches" variant="outline" size="sm">View All</Button>
			</Card.Header>
			<Card.Content>
				{#if data.recentMatches && data.recentMatches.length > 0}
					<div class="space-y-4">
						{#each data.recentMatches as match}
							<div class="flex items-center justify-between rounded-lg border p-3">
								<div class="flex-1">
									<div class="mb-1 flex items-center gap-2">
										<span class="font-medium">{match.map_name}</span>
										<Badge variant="outline">{getMatchScore(match)}</Badge>
									</div>
									<div class="text-sm text-muted-foreground">
										{formatDate(match.created_at)}
									</div>
								</div>
								<Button href="/matches/{match.id}" size="sm" variant="ghost">
									<Eye class="h-4 w-4" />
								</Button>
							</div>
						{/each}
					</div>
				{:else}
					<div class="flex flex-col items-center justify-center py-8 text-center">
						<BarChart3 class="mb-4 h-12 w-12 text-muted-foreground/30" />
						<h3 class="mb-2 font-medium">No matches yet</h3>
						<p class="mb-4 text-sm text-muted-foreground">Upload your first demo to get started</p>
						<Button href="/demos" size="sm">Upload Demo</Button>
					</div>
				{/if}
			</Card.Content>
		</Card.Root>

		<!-- Recent Demo Activity -->
		<Card.Root>
			<Card.Header class="flex flex-row items-center justify-between">
				<div>
					<Card.Title>Demo Activity</Card.Title>
					<Card.Description>Recent demo file uploads and processing</Card.Description>
				</div>
				<Button href="/demos" variant="outline" size="sm">Manage Demos</Button>
			</Card.Header>
			<Card.Content>
				{#if data.recentDemos && data.recentDemos.length > 0}
					<div class="space-y-4">
						{#each data.recentDemos as demo}
							<div class="flex items-center justify-between rounded-lg border p-3">
								<div class="flex flex-1 items-center gap-3">
									<div class="h-2 w-2 rounded-full {getStatusColor(demo.status)}"></div>
									<div class="flex-1">
										<div class="text-sm font-medium">{demo.match_id}</div>
										<div class="text-xs text-muted-foreground">
											{formatDate(demo.created_at)}
										</div>
									</div>
								</div>
								<Badge variant={getStatusVariant(demo.status)}>
									{demo.status}
								</Badge>
							</div>
						{/each}
					</div>
				{:else}
					<div class="flex flex-col items-center justify-center py-8 text-center">
						<FileText class="mb-4 h-12 w-12 text-muted-foreground/30" />
						<h3 class="mb-2 font-medium">No demo files</h3>
						<p class="mb-4 text-sm text-muted-foreground">Upload demo files to see activity here</p>
						<Button href="/demos" size="sm">Upload Demo</Button>
					</div>
				{/if}
			</Card.Content>
		</Card.Root>
	</div>

	<!-- Getting Started Card (only show if no data) -->
	{#if data.stats.totalDemos === 0}
		<Card.Root class="border-dashed">
			<Card.Header>
				<Card.Title class="flex items-center gap-2">
					<Play class="h-5 w-5" />
					Getting Started
				</Card.Title>
			</Card.Header>
			<Card.Content class="space-y-4">
				<p class="text-muted-foreground">
					Welcome to Brainless Stats! Start by uploading your first CS2 demo file to see detailed
					match analytics and player statistics.
				</p>
				<div class="flex gap-3">
					<Button href="/demos" class="gap-2">
						<Upload class="h-4 w-4" />
						Upload Your First Demo
					</Button>
				</div>
			</Card.Content>
		</Card.Root>
	{/if}
</div>

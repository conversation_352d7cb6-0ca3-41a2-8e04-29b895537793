import { redirect } from '@sveltejs/kit';
import type { LayoutServerLoad } from './$types';
import { getUserById } from '$lib/users';

export const load: LayoutServerLoad = async ({ locals, route }) => {
	const user_id = await locals.auth();
	if (!user_id) {
		redirect(302, '/login');
	}

	const user = await getUserById(Number(user_id));

	if (user && !user.has_tracking && route.id !== '/(app)/settings') {
		redirect(302, '/settings');
	}

	return { user };
};

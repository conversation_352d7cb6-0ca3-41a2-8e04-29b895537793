import { constructSteamLoginUrl } from '$lib/steam_auth';
import { redirect, type Actions } from '@sveltejs/kit';
import type { PageServerLoad } from './$types';

export const load: PageServerLoad = async ({ locals }) => {
	if (await locals.auth()) {
		return {
			status: 302,
			headers: {
				location: '/'
			}
		};
	}
};

export const actions: Actions = {
	login_steam: () => {
		const steam_login_url = constructSteamLoginUrl();
		console.log(steam_login_url);
		redirect(302, steam_login_url);
	}
};

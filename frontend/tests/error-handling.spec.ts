import { test, expect } from '@playwright/test';

test.describe('Error <PERSON>ling', () => {
	test('handles 404 pages gracefully', async ({ page }) => {
		// Navigate to a non-existent page
		const response = await page.goto('/non-existent-page');

		// Should show a 404 page or redirect
		expect(response?.status()).toBe(404);
	});

	test('handles invalid match IDs', async ({ page }) => {
		// Try to access a match with invalid ID
		await page.goto('/matches/invalid-id');

		// Should handle the error gracefully
		await expect(page.locator('body')).toBeVisible();
	});

	test('handles network errors gracefully', async ({ page }) => {
		// Simulate offline condition
		await page.context().setOffline(true);

		await page.goto('/matches');

		// Page should still load with cached content or show error message
		await expect(page.locator('body')).toBeVisible();

		// Restore online condition
		await page.context().setOffline(false);
	});
});

test.describe('Performance', () => {
	test('pages load within reasonable time', async ({ page }) => {
		const startTime = Date.now();

		await page.goto('/demos');

		const loadTime = Date.now() - startTime;
		expect(loadTime).toBeLessThan(5000); // 5 seconds max
	});

	test('navigation is fast', async ({ page }) => {
		await page.goto('/demos');

		const startTime = Date.now();
		await page.click('a[href="/matches"]');
		await page.waitForLoadState('networkidle');

		const navigationTime = Date.now() - startTime;
		expect(navigationTime).toBeLessThan(3000); // 3 seconds max
	});
});

test.describe('Accessibility', () => {
	test('pages have proper headings structure', async ({ page }) => {
		await page.goto('/demos');

		// Check for h1
		const h1 = page.locator('h1');
		await expect(h1).toBeVisible();
		await expect(h1).toContainText('Demo Management');

		await page.goto('/matches');

		const matchesH1 = page.locator('h1');
		await expect(matchesH1).toBeVisible();
		await expect(matchesH1).toContainText('Matches');
	});

	test('form inputs have proper labels', async ({ page }) => {
		await page.goto('/demos');

		// Check that form inputs have associated labels
		const matchIdInput = page.getByLabel('Match ID');
		await expect(matchIdInput).toBeVisible();

		const fileInput = page.getByLabel('Demo File');
		await expect(fileInput).toBeVisible();
	});

	test('buttons have descriptive text', async ({ page }) => {
		await page.goto('/demos');

		const uploadButton = page.locator('button[type="submit"]');
		await expect(uploadButton).toContainText('Upload Demo');

		await page.goto('/matches');

		const uploadDemoButton = page.locator('text=Upload Demo');
		await expect(uploadDemoButton).toBeVisible();
	});
});

test.describe('Security', () => {
	test('file input only accepts .dem files', async ({ page }) => {
		await page.goto('/demos');

		const fileInput = page.locator('input[name="demoFile"]');
		await expect(fileInput).toHaveAttribute('accept', '.dem');
	});

	test('form has CSRF protection', async ({ page }) => {
		await page.goto('/demos');

		// Check that the form has proper action and method
		const form = page.locator('form[action*="upload"]');
		await expect(form).toHaveAttribute('method', 'POST');
		await expect(form).toHaveAttribute('enctype', 'multipart/form-data');
	});
});

test.describe('User Experience', () => {
	test('provides feedback during form submission', async ({ page }) => {
		await page.goto('/demos');

		// Fill the form
		await page.fill('input[name="matchId"]', 'test-123');

		// Note: In a real test, we would need to handle the actual file upload
		// For now, just verify the loading state would be shown
		const submitButton = page.locator('button[type="submit"]');
		await expect(submitButton).toContainText('Upload Demo');

		// The button should be disabled when form is invalid or submitting
		// This would need actual form submission to test properly
	});

	test('shows empty states appropriately', async ({ page }) => {
		await page.goto('/demos');
		await expect(page.locator('text=No demo files uploaded yet')).toBeVisible();

		await page.goto('/matches');
		await expect(page.locator('text=No matches processed yet')).toBeVisible();
	});

	test('provides clear navigation paths', async ({ page }) => {
		await page.goto('/matches');

		// From empty matches, user should be able to upload demos
		await expect(page.locator('text=Upload your first demo')).toBeVisible();

		// From header, user can also upload
		await expect(page.locator('text=Upload Demo')).toBeVisible();
	});
});

import { test, expect } from '@playwright/test';

test.describe('Demo Upload Flow', () => {
	test.beforeEach(async ({ page }) => {
		// Navigate to the demo upload page
		await page.goto('/demos');
	});

	test('displays upload form with required fields', async ({ page }) => {
		// Check page title
		await expect(page.locator('h1')).toContainText('Demo Management');

		// Check upload form elements
		await expect(page.locator('text=Upload Demo File')).toBeVisible();
		await expect(page.getByLabel('Match ID')).toBeVisible();
		await expect(page.getByLabel('Demo File')).toBeVisible();
		await expect(page.locator('button[type="submit"]')).toContainText('Upload Demo');
	});

	test('shows validation errors for empty form submission', async ({ page }) => {
		// Try to submit without filling fields
		await page.click('button[type="submit"]');

		// Check for HTML5 validation or form errors
		const matchIdInput = page.getByLabel('Match ID');
		await expect(matchIdInput).toHaveAttribute('required');

		const fileInput = page.getByLabel('Demo File');
		await expect(fileInput).toHaveAttribute('required');
	});

	test('allows user to fill form fields', async ({ page }) => {
		// Fill match ID
		await page.fill('input[name="matchId"]', 'test-match-123');
		await expect(page.locator('input[name="matchId"]')).toHaveValue('test-match-123');

		// Note: File upload testing in Playwright requires actual files
		// For now, just verify the file input exists and is of correct type
		const fileInput = page.locator('input[name="demoFile"]');
		await expect(fileInput).toHaveAttribute('type', 'file');
		await expect(fileInput).toHaveAttribute('accept', '.dem');
	});

	test('displays demo files list section', async ({ page }) => {
		await expect(page.locator('text=Your Demo Files')).toBeVisible();

		// Should show empty state initially
		await expect(page.locator('text=No demo files uploaded yet')).toBeVisible();
	});
});

test.describe('Navigation', () => {
	test('allows navigation between main sections', async ({ page }) => {
		// Start at demos page
		await page.goto('/demos');
		await expect(page.locator('h1')).toContainText('Demo Management');

		// Navigate to matches
		await page.click('a[href="/matches"]');
		await expect(page.locator('h1')).toContainText('Matches');

		// Check for empty state
		await expect(page.locator('text=No matches processed yet')).toBeVisible();

		// Navigate back to demos
		await page.click('text=Upload your first demo');
		await expect(page.url()).toContain('/demos');
	});

	test('sidebar navigation works', async ({ page }) => {
		await page.goto('/');

		// Check if sidebar exists and has expected items
		// Note: Adjust selectors based on your actual sidebar implementation
		await expect(page.locator('[role="navigation"], nav')).toBeVisible();
	});
});

test.describe('Matches Page', () => {
	test.beforeEach(async ({ page }) => {
		await page.goto('/matches');
	});

	test('displays matches page correctly', async ({ page }) => {
		await expect(page.locator('h1')).toContainText('Matches');
		await expect(page.locator('text=Processed Matches')).toBeVisible();
		await expect(page.locator('text=Upload Demo')).toBeVisible();
	});

	test('shows empty state when no matches', async ({ page }) => {
		await expect(page.locator('text=No matches processed yet')).toBeVisible();
		await expect(page.locator('text=Upload your first demo')).toBeVisible();
	});

	test('upload demo button redirects correctly', async ({ page }) => {
		await page.click('text=Upload Demo');
		await expect(page.url()).toContain('/demos');
	});
});

test.describe('Responsive Design', () => {
	test('works on mobile viewport', async ({ page }) => {
		await page.setViewportSize({ width: 375, height: 667 });

		await page.goto('/demos');
		await expect(page.locator('h1')).toBeVisible();
		await expect(page.locator('text=Upload Demo File')).toBeVisible();

		await page.goto('/matches');
		await expect(page.locator('h1')).toBeVisible();
		await expect(page.locator('text=Processed Matches')).toBeVisible();
	});

	test('works on tablet viewport', async ({ page }) => {
		await page.setViewportSize({ width: 768, height: 1024 });

		await page.goto('/demos');
		await expect(page.locator('h1')).toBeVisible();

		await page.goto('/matches');
		await expect(page.locator('h1')).toBeVisible();
	});
});

# Brainless Stats - Frontend

SvelteKit frontend application for CS2 demo analysis platform.

## 🏗️ Architecture

- **SvelteKit**: Modern full-stack framework with SSR and API routes
- **TypeScript**: Type-safe development with full IDE support
- **Tailwind CSS**: Utility-first CSS framework
- **Shadcn/ui**: High-quality component library
- **Drizzle ORM**: Type-safe database operations
- **Playwright**: End-to-end testing
- **Vitest**: Fast unit testing

## 🚀 Quick Start

### Prerequisites

- Node.js 18+
- pnpm 8+

### Development Setup

```bash
# From project root (recommended)
pnpm setup

# Or directly in frontend
cd frontend
pnpm install
```

### Running the Frontend

```bash
# From project root (recommended)
pnpm dev:frontend

# Or using the task runner
node scripts/tasks.mjs dev

# Or directly
cd frontend
pnpm dev
```

The application will be available at <http://localhost:3000>

## 🛠️ Available Scripts

### Development

```bash
pnpm dev                    # Start development server
pnpm dev -- --open         # Start and open in browser
pnpm preview               # Preview production build
```

### Building

```bash
pnpm build                 # Build for production
pnpm check                 # Type checking
pnpm sync                  # Sync SvelteKit files
```

### Testing

```bash
pnpm test                  # Run unit tests
pnpm test:ui               # Run tests with UI
pnpm test:run              # Run tests once
pnpm test:coverage         # Run with coverage
pnpm test:e2e              # Run end-to-end tests
pnpm test:e2e:ui           # Run E2E tests with UI
```

### Code Quality

```bash
pnpm lint                  # Run linting
pnpm lint --fix            # Auto-fix issues
pnpm format                # Format code
```

### API Integration

```bash
pnpm schema:generate       # Generate API types from backend
```

## 📁 Project Structure

```
frontend/
├── src/
│   ├── lib/
│   │   ├── components/     # Reusable UI components
│   │   │   ├── ui/         # Shadcn/ui components
│   │   │   └── app-*.svelte # App-specific components
│   │   ├── api/            # API client and types
│   │   ├── server/         # Server-side code
│   │   │   └── db/         # Database schema and operations
│   │   ├── stores/         # Svelte stores
│   │   └── utils/          # Utility functions
│   ├── routes/             # SvelteKit routes
│   │   ├── (app)/          # Protected app routes
│   │   ├── (public)/       # Public routes
│   │   └── health/         # Health check endpoint
│   ├── app.html            # HTML template
│   └── app.css             # Global styles
├── static/                 # Static assets
├── tests/                  # Test files
├── Dockerfile.dev          # Development container
├── Dockerfile.prod         # Production container
├── package.json            # Dependencies and scripts
└── README.md              # This file
```

## 🎨 UI Components

The frontend uses Shadcn/ui components built on top of Tailwind CSS:

### Available Components

- **Tables**: Data display with sorting and filtering
- **Forms**: Type-safe forms with validation
- **Navigation**: Sidebar, breadcrumbs, and menus
- **Feedback**: Toasts, alerts, and loading states
- **Layout**: Cards, containers, and grids

### Adding New Components

```bash
# Add a new Shadcn component
npx shadcn-svelte@latest add button

# Or add multiple components
npx shadcn-svelte@latest add button input label
```

## 🔧 Configuration

### Environment Variables

```bash
# Database
DATABASE_URL=local.db

# Backend API
BACKEND_API_URL=http://localhost:8000

# Node Environment
NODE_ENV=development
```

### API Integration

The frontend automatically generates TypeScript types from the backend OpenAPI schema:

```bash
# Generate API types (requires backend to be running)
pnpm schema:generate
```

This creates `src/lib/api/schema.d.ts` with all API types.

### Database

The frontend uses its own SQLite database for user sessions and local data:

```bash
# Generate database migrations
pnpm db:generate

# Apply migrations
pnpm db:migrate

# Open database studio
pnpm db:studio
```

## 🧪 Testing

### Unit Tests (Vitest)

```bash
# Run unit tests
pnpm test

# Run with coverage
pnpm test:coverage

# Run in watch mode
pnpm test:watch

# Run with UI
pnpm test:ui
```

### End-to-End Tests (Playwright)

```bash
# Run E2E tests
pnpm test:e2e

# Run with UI
pnpm test:e2e:ui

# Run specific test
pnpm test:e2e tests/login.spec.ts
```

### Test Structure

```
tests/
├── unit/                  # Unit tests
├── integration/           # Integration tests
└── e2e/                   # End-to-end tests
    ├── auth.spec.ts
    ├── dashboard.spec.ts
    └── upload.spec.ts
```

## 🔍 Code Quality

### Linting (ESLint)

```bash
# Check for issues
pnpm lint

# Auto-fix issues
pnpm lint --fix
```

### Formatting (Prettier)

```bash
# Format code
pnpm format

# Check formatting
pnpm format:check
```

### Type Checking

```bash
# Type check
pnpm check

# Type check in watch mode
pnpm check:watch
```

## 🐳 Docker

### Development

```bash
# Build development image
docker build -f Dockerfile.dev -t brainless-stats-frontend:dev .

# Run with hot reloading
docker run -p 3000:3000 -v $(pwd)/src:/app/src brainless-stats-frontend:dev
```

### Production

```bash
# Build production image
docker build -f Dockerfile.prod -t brainless-stats-frontend:prod .

# Run production container
docker run -p 3000:3000 brainless-stats-frontend:prod
```

## 🔗 API Integration

### Type-Safe API Client

```typescript
import client from '$lib/api';

// All API calls are fully typed
const matches = await client.GET('/api/matches');
const user = await client.POST('/api/users/register', {
	body: { username, email }
});
```

### Error Handling

```typescript
import { error } from '@sveltejs/kit';

try {
	const response = await client.GET('/api/matches/{id}', {
		params: { path: { id: matchId } }
	});

	if (response.error) {
		throw error(404, 'Match not found');
	}

	return response.data;
} catch (err) {
	throw error(500, 'Failed to fetch match');
}
```

## 🚀 Deployment

### Production Build

```bash
# Build for production
pnpm build

# Preview production build
pnpm preview
```

### Deployment Checklist

- [ ] Set `NODE_ENV=production`
- [ ] Configure production database
- [ ] Set correct `BACKEND_API_URL`
- [ ] Configure SSL certificates
- [ ] Set up monitoring and logging
- [ ] Configure CDN for static assets

### Health Checks

The frontend provides a health check endpoint at `/health` for load balancers and monitoring.

## 🤝 Contributing

1. Follow the code style (Prettier + ESLint)
1. Add tests for new features
1. Update component documentation
1. Run the full test suite
1. Check type safety

```bash
# Before committing
pnpm lint --fix
pnpm format
pnpm check
pnpm test
pnpm test:e2e
```

## 📚 Resources

- [SvelteKit Documentation](https://kit.svelte.dev/)
- [Svelte Documentation](https://svelte.dev/)
- [Tailwind CSS](https://tailwindcss.com/)
- [Shadcn/ui Svelte](https://www.shadcn-svelte.com/)
- [Playwright Testing](https://playwright.dev/)
- [Vitest Testing](https://vitest.dev/)

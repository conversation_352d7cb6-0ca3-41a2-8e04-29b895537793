Please implement the GitHub issue: $ARGUMENTS using a branch rebase strategy.

Follow these steps carefully:

## 1. Issue Analysis
1. Use `gh issue view $ARGUMENTS` to get the issue details
2. Understand the problem, requirements, and acceptance criteria
3. Identify which packages/files will need changes (frontend, backend, demo-processing, core)
4. Ask clarifying questions if the issue is unclear

## 2. Branch Strategy (Rebase Workflow)
1. Ensure you're on the latest main: `git checkout main && git pull origin main`
2. Create a feature branch: `git checkout -b feature/issue-$ARGUMENTS` or similar descriptive name
3. **IMPORTANT**: Use rebase strategy throughout - NO merge commits

## 3. Planning Phase
1. **Think hard** about the implementation approach
2. Create a detailed plan covering:
   - Database changes (SQLModel models if needed)
   - Backend API changes (FastAPI endpoints)
   - Frontend changes (SvelteKit components/routes)
   - Demo processing changes (if applicable)
   - Testing strategy
3. **Do NOT start coding yet** - confirm the plan first

## 4. Implementation Phase
1. **Write tests first** when possible (TDD approach)
2. Implement changes in logical order:
   - Core/shared utilities first
   - Database models and migrations
   - Backend API endpoints
   - Frontend components and pages
   - Integration between services
3. **Test frequently** during implementation
4. **Commit often** with descriptive conventional commit messages

## 5. Testing & Quality Assurance
1. Run all tests: `pnpm test`
2. Run linting from root: `pnpm lint`
3. Check TypeScript: `cd frontend && pnpm check`
4. Test the feature manually in development environment
5. Verify the implementation meets all acceptance criteria

## 6. Documentation & Cleanup
1. Update relevant documentation if needed
2. Update `CHANGELOG.md` under `[Unreleased]` section
3. Ensure all code follows project style guidelines
4. Clean up any debug code or temporary files

## 7. Final Rebase & Push
1. **Rebase onto latest main**: 
   ```bash
   git fetch origin main
   git rebase origin/main
   ```
2. **Resolve any conflicts** if they arise
3. **Force push** the rebased branch: `git push origin feature/issue-$ARGUMENTS --force-with-lease`

## 8. Pull Request Creation
1. Create PR using: `gh pr create --title "feat: implement issue #$ARGUMENTS" --body "Closes #$ARGUMENTS"`
2. Include in PR description:
   - Summary of changes
   - Testing performed
   - Any breaking changes
   - Screenshots/demos if UI changes

## Important Notes
- **NEVER include** Claude Code attribution in commits
- **Use conventional commit format**: `type(scope): description`
- **Test your changes** before each commit
- **Keep commits focused** - one logical change per commit
- **Rebase, don't merge** - maintain linear history
- **Use SQLModel** for any database model changes
- **Generate frontend types** after backend API changes: `cd frontend && pnpm schema:generate`

## Technology-Specific Guidelines
- **Backend**: Use SQLModel, databases library, FastAPI patterns
- **Frontend**: Use SvelteKit, TypeScript, shadcn/ui components
- **Testing**: Prefer integration tests, use testcontainers when needed
- **Linting**: Always run from repository root

Remember: The goal is a clean, rebased feature branch that can be merged into main without conflicts.

# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

Brainless Stats is a CS2 demo analysis platform built as a monorepo with:

- **Frontend**: SvelteKit + TypeScript application
- **Backend**: FastAPI + Python application
- **Demo Processing**: FastStream microservice for CS2 demo parsing
- **Infrastructure**: Docker, Redis, Nginx

## Development Commands

### Quick Start

```bash
# Initial setup
pnpm setup

# Start development (frontend + backend)
pnpm dev

# Start full Docker environment 
pnpm dev:full
```

### Common Development Tasks

```bash
# Development
pnpm dev:frontend          # Frontend only
pnpm dev:backend          # Backend only  
pnpm dev:demo-processing  # Demo processing service

# Testing
pnpm test                 # All tests
pnpm test:frontend        # Vitest unit tests
pnpm test:backend         # Pytest tests
pnpm test:e2e            # Playwright E2E tests
pnpm test:coverage       # Tests with coverage

# Code Quality
pnpm lint                # Lint all services
pnpm lint:fix           # Auto-fix linting issues
pnpm format             # Format code

# IMPORTANT: Always run linting and type checking from repository root
# The linting and formatting configs are located at the root level

# Building
pnpm build              # Build all services
pnpm build:docker       # Build Docker images

# Versioning & Releases
pnpm version:status     # Check version status across all packages
pnpm version:patch      # Create patch release (0.1.0 → 0.1.1)
pnpm version:minor      # Create minor release (0.1.0 → 0.2.0)
pnpm version:major      # Create major release (0.1.0 → 1.0.0)
```

### Backend-Specific Commands

```bash
cd packages/backend
uv run brainless-cli --help      # Backend CLI help
uv run brainless-cli dev         # Start Docker environment
uv run brainless-cli test        # Run backend tests
uv run brainless-cli lint        # Backend linting
uv run brainless-cli format-code # Backend code formatting
uv run pytest                   # Direct pytest execution
```

### Frontend-Specific Commands

```bash
cd frontend
pnpm dev                      # Dev server
pnpm check                    # Type checking
pnpm test:e2e                # Playwright tests
pnpm schema:generate         # Generate API types from OpenAPI
```

## Architecture

### Monorepo Structure

- **pnpm workspaces** for frontend dependency management
- **uv workspace** for Python packages (backend + demo_processing + core)
- Shared core package at `packages/core`
- Python packages organized under `packages/` directory

### Service Architecture

- **Frontend (port 3000)**: SvelteKit app with its own SQLite database for user sessions
- **Backend API (port 8000)**: FastAPI with SQLite database for application data
- **Demo Processing**: FastStream microservice communicating via Redis
- **Redis (port 6379)**: Message broker for async demo processing

### Key Patterns

- Backend uses **FastAPI** with **Pydantic** models and **SQLAlchemy**
- Frontend uses **SvelteKit** with **TypeScript** and **Drizzle ORM**
- Demo processing uses **awpy** and **demoparser2** libraries
- **OpenAPI** schema generation for type-safe frontend API calls

### Environment Variables

Environment files are structured as:

- `.env.example` (root)
- `packages/backend/.env.example`
- `frontend/.env.example`

## Development Workflow

### Code Style

- **Python**: Uses `ruff` for linting/formatting, `basedpyright` for type checking
- **TypeScript**: Uses `eslint` + `prettier`, follows strict TypeScript config
- Both use Google-style docstrings/comments

### Code Linting Notes

- Always run ruff and basedpyright from the root directory because this is where their settings are located.

### Testing Strategy

- **Frontend**: Vitest for unit tests, Playwright for E2E
- **Backend**: Pytest with async support, testcontainers for integration tests
- **Coverage**: Available for both frontend and backend

### Key Files to Understand

- `packages/backend/src/backend/models.py` - Database models
- `packages/backend/src/backend/schemas.py` - API schemas
- `frontend/src/lib/api/` - API client and types
- `packages/demo-processing/` - Demo processing microservice
- `docker-compose.dev.yml` - Development environment setup

## Docker Development

The project uses Docker Compose for development:

- Hot reloading enabled for all services
- Volume mounts for source code changes
- Health checks for service dependencies
- Separate containers for frontend, backend, demo-processing, and Redis

Access points:

- Frontend: <http://localhost:3000>
- Backend API: <http://localhost:8000>
- API docs: <http://localhost:8000/docs>

## Database Setup

- **Backend**: SQLite at `packages/backend/database.db` for application data
- **Frontend**: SQLite at `frontend/local.db` for user sessions
- Uses SQLAlchemy/Drizzle ORMs respectively
- Database migrations handled by respective ORMs

## User Authentication & Tracking Setup

### Login Flow

The application uses Steam OpenID authentication:

1. **Login**: Users authenticate via Steam (`/login` → Steam OpenID → `/steam-callback`)
1. **New Users**: Automatically redirected to `/settings?setup=tracking` for CS2 configuration
1. **Existing Users**: Checked for tracking configuration; redirected to settings if missing
1. **Configured Users**: Go directly to dashboard

### CS2 Tracking Configuration

Users must configure CS2 tracking in settings:

- **Authentication Code**: From CS2 console `status` command (format: `XXXX-XXXXX-XXXX`)
- **Initial Match Token**: Steam share code from CS2 (format: `CSGO-XXXXX-XXXXX-XXXXX-XXXXX-XXXXX`)
- **Backend API**: `POST /api/users/{user_id}/tracking` stores tracking details
- **Validation**: Zod schemas enforce proper formats

### Key Components

- `frontend/src/routes/steam-callback/+page.server.ts` - Handles Steam auth and redirects
- `frontend/src/routes/(app)/settings/+page.svelte` - CS2 tracking form with Steam help link
- `packages/backend/src/backend/models.py` - User and TrackingDetails models
- Steam help link: <https://help.steampowered.com/en/wizard/HelpWithGameIssue/?appid=730&issueid=128>

## Git Commit Guidelines

### Commit Message Format

- Use conventional commit format: `type(scope): description`
- Types: feat, fix, docs, style, refactor, test, chore
- Keep first line under 72 characters
- Use imperative mood: "add feature" not "added feature"

### Important Notes

- **NEVER** include Claude Code attribution or co-authored-by lines in commits
- Keep commit messages clean and professional
- Focus on the actual changes and their purpose

## Changelog Management

### Keep a Changelog Format

Following [Keep a Changelog v1.0.0](https://keepachangelog.com/en/1.0.0/) standard:

**Structure**: `CHANGELOG.md` with versions in reverse chronological order
**Sections by change type**:

- `Added` - New features
- `Changed` - Existing functionality modifications
- `Deprecated` - Soon-to-be removed features
- `Removed` - Features no longer available
- `Fixed` - Bug corrections
- `Security` - Vulnerability addresses

**Key Practices**:

- Maintain `Unreleased` section for upcoming changes
- Use ISO date format (YYYY-MM-DD)
- Group similar changes together
- Write for humans, not machines
- Make entries concise but descriptive

## Versioning & Release Management

### Unified Versioning System

The project uses a unified versioning system that synchronizes versions across all packages:

- Root `package.json` and `pyproject.toml`
- Frontend `package.json`
- All Python packages in `packages/` directory

### Release Commands

```bash
# Check version status
pnpm version:status

# Create releases (handles everything automatically)
pnpm version:patch      # Bug fixes
pnpm version:minor      # New features
pnpm version:major      # Breaking changes
```

### Release Process

Each release command automatically:

1. Updates all package version files
1. Moves `[Unreleased]` changelog entries to new version section
1. Creates git commit and tag
1. Creates GitHub release with changelog notes
1. Triggers CI/CD to build and publish Docker images

### Manual Version Management

```bash
# Direct script usage
node scripts/version-manager.mjs status
node scripts/version-manager.mjs release patch
node scripts/version-manager.mjs update 1.2.3

# Skip git operations
node scripts/version-manager.mjs release patch --skip-git

# Skip GitHub release
node scripts/version-manager.mjs release minor --skip-github
```

**Important**: Always update `CHANGELOG.md` with entries under `[Unreleased]` before creating releases.
